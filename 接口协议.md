⃞KAlE RIS I nai is

 

 

 

 

 

 

 

***\*Navis\**** ***\*N4\****

***\*ECN4\****

 

***\*XMLRDT\****

 

Version 4.0.21

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1.png) 

 

 

 

 

 

 

 

 

 

 

 

Published: Wednesday, November 6, 2024



UNAUTHORIZED USE OF KALERIS* CONFIDENTIAL INFORMATION IS STRICTLY PROHIBITED © 2024 Kaleris. All rights reserved. All trademarks are the property of their respective owners.

This document contains confidential proprietary information protected by agreements and federal copyright law. It may not be disclosed, copied, or reproduced without <PERSON><PERSON>is's prior written authorization.

This document and its Information are subject to change without notice.

*Kaleris is a trademark/trade name for Kaleris entities, including: Navis LP, PINC Solutions, ShipXpress LLC, Navis Ger- many GmbH.



***\*Contents\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2.png) 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps3.png) 

[***\*1 XMLRDT\**** ***\*Functional Overview\****](#bookmark2)   [***\*6\****](#bookmark3)

[1.1 N4 Landscape with ECN4](#bookmark4)   [7](#bookmark5)

[1.1.1 ECN4 XMLRDT Layers](#bookmark6)   [7](#bookmark7)

[1.1.2 Custom XMLRDT Client](#bookmark8)   [8](#bookmark9)

[1.2 EC Entities](#bookmark10)   [10](#bookmark11)

[1.3 XMLRDT Channel](#bookmark12)   [11](#bookmark13)

[1.4 ECN4 Message Envelope](#bookmark14)   [12](#bookmark15)

[***\*2\**** ***\*ECN4 XMLRDT\**** ***\*Message\****](#bookmark16)   [***\*13\****](#bookmark17)

[2.1 ECN4 Message Types](#bookmark18)   [14](#bookmark19)

[2.2 ECN4 Actions](#bookmark20)   [15](#bookmark21)

[2.3 Anatomy of XMLRDT Response from ECN4](#bookmark22)   [17](#bookmark23)

[2.3.1 Pool](#bookmark24)   [17](#bookmark25)

[2.3.2 Work](#bookmark26)   [18](#bookmark27)

[2.3.3 Job List](#bookmark28)   [19](#bookmark29)

[2.3.3.1 Job List            nVMTResponsefor](#bookmark30)   [20](#bookmark31)

[2.3.4 Option-list](#bookmark32)   [21](#bookmark33)

[2.3.5 Acknowledgements](#bookmark34)   [22](#bookmark35)

[2.3.6 Errors](#bookmark36)   [23](#bookmark37)

[2.3.7 Attributes](#bookmark38)   [23](#bookmark39)

[2.4 Example XMLRDT Messages](#bookmark40)   [26](#bookmark41)

[2.4.1 Example Availability Messages](#bookmark42)   [27](#bookmark43)

[2.4.1.1 CHE Log In 2632](#bookmark44)   [27](#bookmark45)

[******* CHE Become Available 2632](#bookmark46)   [28](#bookmark47)

[2.4.1.3 CHE Trailer Selection 2632](#bookmark48)   [29](#bookmark49)

[******* CHE Become Unavailable 2632](#bookmark50)   [31](#bookmark51)

[******* CHE Log Off2632](#bookmark52)   [32](#bookmark53)

[2.4.1.6 Area Status 2409](#bookmark54)   [32](#bookmark55)

[2.4.1.7 Ping Message2509](#bookmark56)   [34](#bookmark57)

[2.4.1.8 CHE Status 2609](#bookmark58)   [35](#bookmark59)

[2.4.1.9 CHE Cancel 2630](#bookmark60)   [35](#bookmark61)

[2.4.1.10 Surrogate CHE Selection](#bookmark62)   [37](#bookmark63)

[2.4.1.11 CHE Clear Message2630](#bookmark64)   [38](#bookmark65)

[2.4.1.12 OTR Container        ParkInventory 2709](#bookmark66)    [39](#bookmark67)

[2.4.2 Example CHE Messages](#bookmark68)   [42](#bookmark69)

[2.4.2.1 CHE Lift](#bookmark70)   [42](#bookmark71)

[2.4.2.2 CHE Confirm Container 2631](#bookmark72)   [50](#bookmark73)

[2.4.2.3 CHE Set](#bookmark74)   [52](#bookmark75)

 

 

[**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**](#bookmark76)



![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps4.png)[2.4.2.4 CHE Auto Rehandle](#bookmark78)   [56](#bookmark79)

[2.4.2.5 CHE Manual Rehandle 2630](#bookmark80)   [57](#bookmark81)

[2.4.2.6 CHE Progress Update](#bookmark82)   [59](#bookmark83)

[2.4.2.7 CHE Joblist Request 2724](#bookmark84)   [60](#bookmark85)

[2.4.2.8 CHE Joblist Select 2630](#bookmark86)   [64](#bookmark87)

[2.4.2.9 CHE Truck Departure2630](#bookmark88)   [66](#bookmark89)

[2.4.2.10 CHE Joblist Promotion 2535](#bookmark90)   [67](#bookmark91)

[2.4.2.11 CHE Show Joblist Filter 2727](#bookmark92)   [67](#bookmark93)

[2.4.2.12 CHE Show Joblist Sort 2727](#bookmark94)   [68](#bookmark95)

[2.4.2.13 CHE Select Empty Delivery 2630](#bookmark96)   [69](#bookmark97)

[******** CHE Empty to Origin](#bookmark98)   [70](#bookmark99)

[2.4.2.15 CHE TBD Merge](#bookmark100)   [71](#bookmark101)

[2.4.2.16 CHE Dispatch TT](#bookmark102)   [73](#bookmark103)

[2.4.2.17 CHE Search Truck Jobs](#bookmark104)   [74](#bookmark105)

[2.4.2.18 CHE Swap Position](#bookmark106)   [74](#bookmark107)

[2.4.2.19 CHE ChangeListMode 2727](#bookmark108)   [76](#bookmark109)

[2.4.2.20 CHE Self-Assign2640](#bookmark110)   [79](#bookmark111)

[2.4.3 Example TT Messages](#bookmark112)   [80](#bookmark113)

[2.4.3.1 CHE Pull Trailer 2630](#bookmark114)   [81](#bookmark115)

[2.4.3.2 CHE Park Trailer 2630](#bookmark116)   [85](#bookmark117)

[2.4.3.3 CHE Park And Wait](#bookmark118)   [88](#bookmark119)

[******* CHE TT Progress Update2630 and 2633](#bookmark120)   [92](#bookmark121)

[******* CHE TT Signals Container Lifted](#bookmark122)   [97](#bookmark123)

[2.4.3.6 CHE TT lane information for vesselload and discharge](#bookmark124)   [99](#bookmark125)

[***\*3 Geodetic\**** ***\*Positions\****](#bookmark126)   [***\*101\****](#bookmark127)

[3.1 Example Geodetic Messages](#bookmark128)   [102](#bookmark129)

[3.2 CHE Coordinate Conversion (not supported)](#bookmark130)   [108](#bookmark131)

[3.3 Lift      Truck      Spatial Bin Filteringfromand](#bookmark132)   [113](#bookmark133)

[3.4 Tier Calculation](#bookmark134)   [114](#bookmark135)

[3.5 Geodetic Configuration](#bookmark136)   [116](#bookmark137)

[***\*4\**** ***\*refID\**** ***\*Positions\****](#bookmark138)   [***\*117\****](#bookmark139)

[4.1 refID Syntax](#bookmark140)   [118](#bookmark141)

[4.2 Special use cases          refIDconcerning](#bookmark142)   [120](#bookmark143)

[4.3 Example refId Messages](#bookmark144)   [122](#bookmark145)

[4.3.1 Example refId Lift](#bookmark146)   [122](#bookmark147)

[4.3.2 Example refID Set](#bookmark148)   [122](#bookmark149)

[4.3.3 Example refID Position Update](#bookmark150)   [123](#bookmark151)

[4.3.4 Example Terminal Tractor Messages       refIDusing](#bookmark152)   [123](#bookmark153)

[4.3.5 Example Truck (OTR) Messages using refID](#bookmark154)   [124](#bookmark155)

[4.3.6 Fail to Deck responses using refId](#bookmark156)   [126](#bookmark157)

 

 

[**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**](#bookmark158)



![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps5.png)[4.4 refID Configuration](#bookmark159)   [127](#bookmark160)

[***\*5\**** ***\*Narrowband Serial\**** ***\*PDS\****](#bookmark161)   [***\*128\****](#bookmark162)

[5.1 Narrowband Support for ECN4 (ANSI Terminal)](#bookmark163)   [129](#bookmark164)

[5.2 Narrowband Components    Configurationand](#bookmark165)   [130](#bookmark166)

[5.3 Narrowband User Interface](#bookmark167)   [131](#bookmark168)

[5.4 Serial PDS](#bookmark169)   [133](#bookmark170)

[5.4.1 Serial Message Format](#bookmark171)   [133](#bookmark172)

[5.4.1.1 Message format for PDS to RDT messages](#bookmark173)   [133](#bookmark174)

[5.4.1.2 Container position elements (message types “1” – “5”)](#bookmark175)   [134](#bookmark176)

[5.4.1.3 Message format for RDT to PDS messages](#bookmark177)   [134](#bookmark178)

[******* Message Type](#bookmark179)   [135](#bookmark180)

[5.4.1.5 Yard Stack Name](#bookmark181)   [135](#bookmark182)

[******* Spreader Mode](#bookmark183)   [135](#bookmark184)

[******* Serial       DelimitterInput](#bookmark185)   [136](#bookmark186)

[5.4.1.826-Code](#bookmark187)   [136](#bookmark188)

[***\*6\**** ***\*Updating Yard\**** ***\*Measured Weight\****](#bookmark189)   [***\*137\****](#bookmark190)

[6.1 Container Scales    Weightand](#bookmark191)   [138](#bookmark192)

[6.2 UpdatingYard Measured Weight](#bookmark193)   [139](#bookmark194)

[6.3 Example Yard Measured Weight Messages](#bookmark195)   [140](#bookmark196)

[6.4 Yard Measured Weight Configuration](#bookmark197)   [142](#bookmark198)

[***\*7\**** ***\*Index\****](#bookmark199)   [***\*143\****](#bookmark200)

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**1** **XMLRDT** **Functional Overview**

 

 

 

***\*1 XMLRDT\**** ***\*Functional Overview\****      

XMLRDT is a Navis branded communication protocol that serves as the interface between subsystems of Navis Equip- ment Control (ECN4). The XMLRDT protocol is vendor-neutral, because it specifies interactions only at the interface,  and not interactions internal third-party radio equipment. The messages communicate the state of the actors in sup-

ported EC processes, but not specifically how information is presented.

XMLRDT does all of its communication over a standard TCP/IP network for ease of use and simplicity. It also uses XML payloads to allow for program readable and verifiable schema's which will allow for faster and flexible implementations.

In This Section

l [N4 Landscape with ECN4](#bookmark201)  [***\*7\****](#bookmark202)

l [EC Entities](#bookmark203)  [***\*10\****](#bookmark204)

l [XMLRDT Channel](#bookmark205)  [***\*11\****](#bookmark206)

l [ECN4 Message Envelope](#bookmark207)  [***\*12\****](#bookmark208)

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**1** **XMLRDT** **Functional Overview**

**1.1** **N4 Landscape with ECN4**

 

 

***\*1.1\**** ***\*N4\**** ***\*Landscape with\**** ***\*ECN4\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps6.png) 

Of special interest to parties familiar with the SPARCS radio server is that clerking applications (RDT) have been moved to a new layer as well, and do not use the XMLRDT API. Please see the N4 Mobile product information for details on

applications like the Hatch Clerk, Inspections, and Yard Inventory.

***\*1.1.1\**** ***\*ECN\*******\*4\**** ***\*XMLRDT\**** ***\*Layers\****

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**1.1** **N4 Landscape with ECN4**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps7.png) 

Equipment Control follows a 3-tier architecture, consisting of

n ECN4 as the primary hub for equipment state management and communication node for incoming and outgoing messages to CHE and the core N4 system.

n ECN4Web as a Navis provided web server that transforms the XMLRDT messages into the user interfaces.

n A non-Navis radio system layer with RDT server and the associated RDTs (which represent container handling equipment (CHE) or direct browser connections)

 

***\*1.1.2\**** ***\*Custom\**** ***\*XMLRDT\**** ***\*Client\****

Third parties can write applications that talk to ECN4 using the XMLRDT API in the same manner than ECN4Web does. These third party systems can be deployed along, or instead of, ECN4Web. Messages generated by ECN4 are dis-

tributed to all systems connected to its port.

This is typically used when a PDS system does not provide a user interface.

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**1.1** **N4 Landscape with ECN4**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps8.png) 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**1** **XMLRDT** **Functional Overview**

**1.2** **EC Entities**

 

 

***\*1.2\**** ***\*EC\**** ***\*Entities\****

The primary EC entities involved in message flows are **container** **handling equipment** (CHE),**jobs** (which can be

delivered lists), and **containers**. These entities are related as follows: a specific **CHE** is the internal representation for the Navis XPS entity which executes a**job** to move a **container** to a specified location within a terminal or group of ter- minals.

A **radio data terminal** (RDT) represents a CHE in that the RDT is the mechanism for the exchange of information with CHE operators and the containers they are moving.

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**1** **XMLRDT** **Functional Overview**

**1.3** **XM****LRDT** **Channe****l**

 

 

***\*1.3\**** ***\*XMLRDT\**** ***\*Channel\****

The XMLRDT channel is typically kept open all the time between ECN4 and the XMLRDT clients. This allows ECN4 to notify all the client systems of important changes. This means when one XMLRDT Client sends an message to ECN4, the resulting response will be sent to all XMLRDT Clients, such that all clients will receive the latest response from

ECN4.

The XMLRDT channel is essentially a stream of message envelope being exchanged in both directions, and the applic- ations at both end must read and process all envelope as they arrive.

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**1** **XMLRDT** **Functional Overview**

**1.4** **ECN4 Message Envelope**

 

 

***\*1.4\**** ***\*ECN4\**** ***\*Message\**** ***\*Envelope\****

All XMLRDT messages sent to and from ECN4 must conform to the message envelope. The message envelope is defined as:

 

| ***\*Field\****    | ***\*Value\**** | ***\*Size\**** ***\*in\*******\*bytes\**** | ***\*Notes\****                                              |
| ------------------ | --------------- | ------------------------------------------ | ------------------------------------------------------------ |
| Message length + 2 |                 | 2                                          | Length in bytes of the XMLRDT message + 2, encoded as a two-byte, big- endian number. |
| Message ID         | 8001            | 2                                          | Message id, encoded as a two-byte, big-endian number.        |
| Message            |                 |                                            | The XMLRDT message, using UTF-8 character encoding.          |
| EOF                | 0xff            | 1                                          | End of the message envelope.                                 |

The maximum message length is 20,000 bytes. The "Message ID" in the XMLRDT envelope is always 8001 for mes-

sages sent to and from ECN4. If ECN4 receives a message with a "Message ID" other than 8001 it will treat this as a cor- rupted message and log an error accordingly.

For example if the XMLRDT message to be sent to ECN4 is:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps9.png)

In UTF-8 format, the message length is 32 bytes.   The hexadecimal bytes sent over the wire would be

 

| ***\*Hex\****                                                | ***\*Field\****    | ***\*Value\****                  |
| ------------------------------------------------------------ | ------------------ | -------------------------------- |
| 00 22                                                        | Message length + 2 | 34                               |
| 1f 41                                                        | Message ID         | 8001                             |
| 3c 6d 65 73 73 61 67 65 20 74 79 70 65 3d 22 32 35 30 39 22 20 4d 53 49 44 3d 22 31 22 20 2f 3e | Message            | <message type="2509" MSID="1" /> |
| ff                                                           | End of message     | 0xff                             |

The prolog is an optional part of XMLRDT as defined by the xml specification. It can contain among others an xml declar- ation <?xml version="1.0" encoding="UTF-8"?> or a document type <!DOCTYPE..>, can safely be omitted from the

transmitted message.

The formId attribute, present on the message node, should be sent only from XMLRDT clients that use a form UI. Sub- missions from forms are subject to guards against multiple rapid submissions and systems which must simply report   events, like PDS reporting lifts, sets, and position updates must not submit XMLRDT requests with the formId attribute, as these safe guards may present errors in the full deployed system.

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2** **ECN4** **XMLRDT** **Message**

**1.4** **ECN4 Message Envelope**

 

 

***\*2\**** ***\*ECN4 XMLRDT\**** ***\*Message\****          

This section contains information on what is in the XMLRDT message. This information is taken from the

xmlRdtReqest.xsd, xmlRdtResponse.xsd, and xmlRdtCommon.xsd files that are part of this SDK package and listed here for convenience, though the information below does not cover every node/attribute detailed in the xsd files.

In This Section

l [ECN4 Message Types](#bookmark210)  [***\*14\****](#bookmark211)

l [ECN4 Actions](#bookmark212)  [***\*15\****](#bookmark213)

l [Anatomy ofXMLRDT             ECN4Responsefrom](#bookmark214)  [***\*17\****](#bookmark215)

l [Example XMLRDT Messages](#bookmark216)  [***\*26\****](#bookmark217)

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2** **ECN4** **XMLRDT** **Message**

**2.1** **ECN4 Message** **Type****s**

 

 

***\*2.1\**** ***\*ECN\*******\*4\**** ***\*Message\**** ***\*Types\****

Below are the message types supported with XMLRDT:

 

| ***\*Message\**** | ***\*Comments\****                                           |
| ----------------- | ------------------------------------------------------------ |
| 2409              | This area status message is used to set/clear the "TEMP BLOCK" stack status state on selected yard slot positions. |
| 2509              | This is a generic ECN4 XMLRDT Message (ping) to find out if EC is active. If sent for a particular CHE, then the current state of the CHE is returned in response to this message. |
| 2630              | This message is used perform various operations on the CHE such as job assignment, lift, set, progress update, and rehandles.Both 2630 and 2635 can be use interchangeably. |
| 2632              | This message is used to perform various operations such as log in, log out, available, unavailable. |
| 2633              | This message is used to perform a TT progress update with lane confirmation. |
| 2635              | Both 2630 and 2635 can be use interchangeably.               |
| 2631              | Container Confirmation request, to be used in response to a FORM_CONFIRM_CONTAINER response. |
| 2724              | CHE job list request.                                        |
| 2727              | CHE change list mode, job list filter, and job list sorter request. |

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2** **ECN4** **XMLRDT** **Message**

**2.****2** **ECN4** **Action****s**

 

 

***\*2.2\**** ***\*ECN\*******\*4\**** ***\*Actions\****

ECN4 controls CHE workflows through the stateModel.xml file. The workflows described in this section are the default supported workflows, they correspond to the stateModel.xml file in ECN4's classpath that is set by default. A copy of   this stateModel.xml file is included for convenience as it can be read to understand how CHE's formIds transition when certain actions are performed. Customizations can be done to these workflows and there are some examples of these in the Kaleris Community Portal ECN4 Corner Blog Posts included in this SDK for reference. In this section, the illus-  trations provided to detail the workflow will refer to actions defined in the statemodel.xml file. For example: you will see illustrations using the "drop" action and it should be understood that this corresponds to the "Set" action. The below

tables describe some of the information found int he xmlrdtRequest.xsd.

The table below describes the XMLRDT action and its corresponding statemodel action with comments:

***\*Che Action Types for 2630/2635\****

 

| ***\*Action\****       | ***\*Short\*******\*Name\**** | ***\*Description\****                                  | ***\*statemodel.xml\**** ***\*action\**** | ***\*Comments\****                                           |
| ---------------------- | ----------------------------- | ------------------------------------------------------ | ----------------------------------------- | ------------------------------------------------------------ |
| Accept                 | A                             | Job Assign- ment                                       | dispatch                                  |                                                              |
| ProgressUpdate         | B                             | Job has pro-gressed (used when in jobstep mode)        | updatePosition                            |                                                              |
| Complete               | C                             | Complete a job                                         | complete                                  |                                                              |
| Set                    | D                             | Set message (single ortwin)                            | drop                                      |                                                              |
| DispatchITV            | I                             | Dispatch Job to ITV                                    | dispatchITV                               |                                                              |
| ClearMessage           | K                             | Clear textmessage                                      | clearMessage                              |                                                              |
| Lift                   | L                             | Lift message (single ortwin)                           | lift                                      |                                                              |
| ManualRehandle         | M                             | Request amanual yard shift                             | manualRehandle                            |                                                              |
| SelectEmptyDeliveryJob |                               | Explicitly spe-cify containerfor an empty delivery job | selectEmptyDeliveryJob                    |                                                              |
| Reroute                | S                             | Request a reroute                                      |                                           | Deprecated in 2.6. The Set mes- sage should be used instead. |
| SwapPosition           | T                             | Request atwin plannedposition swap                     | swapPosition                              |                                                              |
| Cancel                 | X                             | Job can-cellation                                      | cancel                                    |                                                              |
| PromoteJob             | J                             | Promote a job to the top of  the job list              | promoteJob                                |                                                              |
| Pull                   | U                             | Pull a chassis from a pos-   ition                     | pull                                      |                                                              |

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2.****2** **ECN4** **Action****s**

 

 

| ***\*Action\**** | ***\*Short\*******\*Name\**** | ***\*Description\****                              | ***\*statemodel.xml\**** ***\*action\**** | ***\*Comments\****                                           |
| ---------------- | ----------------------------- | -------------------------------------------------- | ----------------------------------------- | ------------------------------------------------------------ |
| Park             | R                             | Park achassis at a position                        | park                                      |                                                              |
| ParkAndWait      | W                             | Park and waitcoupled at a position                 | parkAndWait                               | Added in 3.1                                                 |
| Refresh          | Refresh                       | Screenrefresh                                      | refresh                                   | This is an internally used action  not intended for XMLRDT Clients to use. It is used to push theinternal state of TTs in reaction to movements from CHE, forinstance CHE set of a container onto a TT. |
| Merge            | G                             | Merge a Con-tainer with a swappable  TBD job       | merge                                     |                                                              |
| Pds              | P                             | Serial PDS message                                 |                                           | Not handled by the statemodel as these messages are transformed into lift, set, and progress update messages before being pro-cessed. See the ***\*Serial\**** ***\*PDS\**** sec- tion for more details. |
| changeListMode   | changeListMode                | Toggle con-tainer list from 'single' to'TWIN' mode | changeListMode                            |                                                              |
| Search           | Search                        | Search forWork Instruc- tions for aTruck           | search                                    |                                                              |
| AreaStatus       | AreaStatus                    | Reports AreaStack Status                           |                                           | This message is intended for use  by automated equipment to notify the TOS of areas blocked by auto- mation systems. It does not inter- act with the statemodel.xml file. |
| updateUnitWeight | Y                             | Update theyard weight for a unit                   | updateUnitWeight                          |                                                              |

***\*User Availability Types for 2632\****

 

| ***\*Action\****   | ***\*Short\*******\*Name\**** | ***\*Description\****                            | ***\*statemodel.xml action\**** |
| ------------------ | ----------------------------- | ------------------------------------------------ | ------------------------------- |
| Log in             | L                             | Log in and become unavailable                    | login                           |
| Log Out            | X                             | Cancel jobs and log out                          | logoff                          |
| Become unavailable | U                             | Become unavailable after completion of next move | becomeUnavailable               |
| Become available   | A                             | Become available                                 | becomeAvailable                 |
| Clear Message      | K                             | Clear old messages from the VMT/ECN4 Web         | ClearMessage                    |

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2** **ECN4** **XMLRDT** **Message**

**2.3** **Anatomy** **ofXMLRDT** **Response** **from** **ECN****4**

 

 

***\*2.3\**** ***\*Anatomy\**** ***\*of\**** ***\*XMLRDT\**** ***\*Response\**** ***\*from\**** ***\*ECN4\****

There are 3 main formats of messages that ECN4 sends as responses to XMLRDT clients: pool, work, and option-list. In This Section

l [Pool](#bookmark218)  [***\*17\****](#bookmark219)

l [Work](#bookmark220)  [***\*18\****](#bookmark221)

l [Job List](#bookmark222)  [***\*19\****](#bookmark223)

l [Option-list](#bookmark224)  [***\*21\****](#bookmark225)

l [Acknowledgements](#bookmark226)  [***\*22\****](#bookmark227)

l [Errors](#bookmark228)  [***\*23\****](#bookmark229)

l [Attributes](#bookmark230)  [***\*23\****](#bookmark231)

 

 

***\*2.3.1\**** ***\*Pool\****

Below is a structure of a typical job message:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps10.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps11.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps12.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps13.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps14.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps15.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps16.png)

Below is an example of a message with only the pool information and no work as the CHE is unavailable:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps17.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps18.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps19.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps20.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps21.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps22.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps23.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps24.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps25.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps26.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps27.png)

The ***\*pool\**** is the below fragment:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps28.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps29.png)

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps30.png)**2.3**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps31.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps32.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps33.png)

Every message form except ones that use an option-list use the pool fragment.

***\*2.3.2\**** ***\*Work\****

Below is a structure of a typical work message:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps34.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps35.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps36.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps37.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps38.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps39.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps40.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps41.png)

Below is an example of the work message

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps42.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps43.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps44.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps45.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps46.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps47.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps48.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps49.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps50.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps51.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps52.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps53.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps54.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps55.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps56.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps57.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps58.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps59.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps60.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps61.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps62.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps63.png)

The formID is: FORM_EMPTY_TO_ORIGIN. The ***\*work\**** fragment is shown below:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps64.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps65.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps66.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps67.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps68.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps69.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps70.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps71.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps72.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps73.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps74.png)

The following forms use the work fragment:

n FORM_EMPTY_TO_ORIGIN

n FORM_EMPTY_AT_ORIGIN

n FORM_LADEN_TO_DEST

n FORM_LADEN_AT_DEST

n FORM_DISPATCH

n FORM_DISPATCH_LIFTED

n FORM_REHANDLE

n FORM_AUTO_REHANDLE

n FORM_REHANDLE_NO_SLOT

n FORM_TRUCK_PULL_TRAILER

n FORM_TRUCK_EMPTY_TO_ORIGIN

n FORM_TRUCK_EMPTY_AT_ORIGIN

n FORM_TRUCK_LADEN_TO_DEST

n FORM_TRUCK_LADEN_AT_DEST

n FORM_TRUCK_PARK_TRAILER

n FORM_CONFIRM_TRUCK_LANE

 

***\*2.3.3\**** ***\*Job\**** ***\*List\****

Below is a structure of a typical work message:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps75.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps76.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps77.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps78.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps79.png)***\*joblist\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps80.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps81.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps82.png)

Below is an example of an job list message. The joblist is comprised of jobs, which resemble the job node under the work fragment, but much less ECN4 XMLRDT Message#verbose.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps83.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps84.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps85.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps86.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps87.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps88.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps89.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps90.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps91.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps92.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps93.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps94.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps95.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps96.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps97.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps98.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps99.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps100.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps101.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps102.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps103.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps104.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps105.png) 

 

***\*2.3.3.1 Job\**** ***\*List\**** ***\*Response for nVMT\****

When using the nVMT application, the CHE's last known position is displayed in a section view of the block.

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps106.png) 

This is made possible by including the refId attribute in the CHE Job List payload, where the refID represent the last   known position of the CHE as a concatenation of block/column/row/tier. If the position is empty or blank, then the refID will not be output.

Here's an example of a refID attribute for a CHE:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps107.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps108.png)

 

 

***\*2.3.4\**** ***\*Option\*******\*-\*******\*list\****

Below is a structure of a typical option-list message:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps109.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps110.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps111.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps112.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps113.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps114.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps115.png)

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

Below is an example of an option-list message. The fromID here is FORM_CONFIRM_CONTAINER. In the below example the option-list is a list of units.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps116.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps117.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps118.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps119.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps120.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps121.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps122.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps123.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps124.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps125.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps126.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps127.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps128.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps129.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps130.png)

The list of units is typical if the option-list is presenting containers, but it may also present options, such as in this example:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps131.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps132.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps133.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps134.png)

 <option index="2" name="Mock Joblist Filter A" label="Mock Joblist Filter A"/>    

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps135.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps136.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps137.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps138.png)

The following forms use the option-list:

n FORM_SELECT_JOB_LIST_SORT

n FORM_SELECT_JOB_LIST_FILTER

n FORM_CONFIRM_CONTAINER

n FORM_TBDUNIT_CONFIRM_CONTAINER

n FORM_SELECT_TRUCK_JOB

 

***\*2.3.5\**** ***\*Acknowledgements\****

Every connected XMLRDT Client is pushed an update when the formID is refreshed. This is the mechanism that keeps all views of the CHE's state in sync, whether in the yard on a CHE VMT, or in the office viewing the screen. When mes-

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

sages are pushed to the XMLRDT Clients in response to an incoming XMLRDT request, the response will contain the ack="Y" attribute.

***\*2.3.6\**** ***\*Errors\****

There are many causes of errors in ECN4. For example, if on FORM_LOGIN an attempt to login a user that is not defined in the system is made:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps139.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps140.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps141.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps142.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps143.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps144.png)

The result will be a push of the same form with an error message:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps145.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps146.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps147.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps148.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps149.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps150.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps151.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps152.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps153.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps154.png)

There may also be an error present in the errmsg node, for example in response to a CHE Status message:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps155.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps156.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps157.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps158.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps159.png)

Previous versions ofthis SDK included sample error messages in the example messages, however, this has now been removed as the error conditions are the same form with various error messages displayed. To learn more about the

error messages see the EC Install and Setup Guide. This will also give you details on rewording or translating these messages for your users.

***\*2.3.7\**** ***\*Attributes\****

The information described in this section is provided for convenience, but the full description of elements and attributes is defined in xmlRdtResponse.xsd and the xmlRdtRequest.xsd. The messages provided in the section may not contain all of the attributes of the messages in the latest version of N4, as Navis will add new attributes to the messages to con- vey more information to XMLRDT Clients, without necessarily updating all example messages and the xmlRdtRe-

sponse.xsd should be used to understand the attributes in the response messages.

When job nodes are created for dispatch forms (FORM_DISPATCH, FORM_EMPTY_TO_ORIGIN, FORM_TRUCK_ EMPTY_TO_ORIGIN, etc.) the verbose attributes are used. When the container node is created for job lists or option lists the verbose attributes are not present.

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

***\*Attributes\**** ***\*included\**** ***\*in "verbose" container node\****

 

| ***\*Attribute Code\**** | ***\*Description\****                                        |
| ------------------------ | ------------------------------------------------------------ |
| EQTP                     | The equipment type                                           |
| HGHT                     | The physical height of the container in millimeters          |
| LOPR                     | The line operator                                            |
| TRKC                     | The trucking company                                         |
| ACRR                     | The arrival visit                                            |
| DCRR                     | The departure visit                                          |
| RLSE                     | The hold type on the container                               |
| RFRT                     | The reefer temperature setting                               |
| CCON                     | The container condition                                      |
| ISHZ                     | Boolean: "Y" if the container has one or more Hazard records |
| DWTS                     | The dwell time, the number of days the container has been in the yard |
| ISGP                     | The container ISO group code                                 |
| GRAD                     | The container grade                                          |
| RMRK                     | Container Selection remarks                                  |
| OD                       | Boolean: "Y" if container is over-dimension                  |
| ODMH                     | The over dimension height defined in centimeters             |
| ODMR                     | The over dimension right defined in centimeters              |
| ODML                     | The over dimension left defined in centimeters               |
| ODMF                     | The over dimension forward defined in centimeters            |
| ODMA                     | The over dimension after defined in centimeters              |

***\*Attributes\**** ***\*included\**** ***\*in container node\****

 

| ***\*Attribute\*******\*Code\**** | ***\*Description\****                                        | ***\*Comment\****                                            |
| --------------------------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| EQID                              | The equipment id                                             |                                                              |
| LNTH                              | The nominal length of the container in feet                  |                                                              |
| QWGT                              | The weight in kilograms                                      |                                                              |
| MNRS                              | The container Maintenance and Repair Status                  |                                                              |
| QCAT                              | The container category                                       | See Container Category                                       |
| onPower                           | Boolean: "Y" if the reefer is on-power, "N" is it is off- power. |                                                              |
| desireOnPower                     | Boolean: "Y" if the reefer needs to become on- power, "N" if it needs to become off-power. |                                                              |
| isTrailer                         | Boolean: "Y" if the container is a trailer (chassis, bombcart or cassette). | l Qualifiers:l 'C'=Chassisl 'S'=Cassettel 'T'=Trailerl 'K'=Bombcart |
| isDamage                          | Boolean: "Y" if the container is a damaged                   |                                                              |
| EQTP                              | The equipment type                                           | Only on non-verbose XMLRDT messages when job is a swappable delivery |
| LOPR                              | The line operator                                            | Only on non-verbose XMLRDT messages when job is a swappable delivery |
| ITRN                              | The inbound train visit                                      | Only on non-verbose XMLRDT messages when job is move kind RLOD or RDSC |
| ORTN                              | The outbound train visit                                     | Only on non-verbose XMLRDT messages when job is move kind RLOD or RDSC |

 

 



**Confidential** **and** **Propriet****ary**



**N4 4.0.21: ECN4**



 

 

***\*Container Category\****

 

| ***\*Code\**** | ***\*Description\**** |
| -------------- | --------------------- |
| D              | Depot                 |
| E              | Export                |
| I              | Import                |
| R              | Restow                |
| M              | Storage               |
| S              | Through               |
| T              | Transship             |
| G              | Transit               |
| ?              | Unknown               |

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2** **ECN4** **XMLRDT** **Message**

**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

***\*2.4\**** ***\*Example\**** ***\*XMLRDT\**** ***\*Messages\****

In This Section

l [Example Availability Messages](#bookmark233)  [***\*27\****](#bookmark234)

l [Example CHE Messages](#bookmark235)  [***\*42\****](#bookmark236)

l [Example TT Messages](#bookmark237)  [***\*80\****](#bookmark238)

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

***\*2.4.1\**** ***\*Example\**** ***\*Availability\**** ***\*Messages\****

l [CHE Log In2632](#bookmark239)  [***\*27\****](#bookmark240)

l [CHE Become Available 2632](#bookmark241)  [***\*28\****](#bookmark242)

l [CHE Trailer Selection 2632](#bookmark243)  [***\*29\****](#bookmark244)

l [CHE Become Unavailable 2632](#bookmark245)  [***\*31\****](#bookmark246)

l [CHE Log Off2632](#bookmark247)  [***\*32\****](#bookmark248)

l [Area Status 2409](#bookmark249)  [***\*32\****](#bookmark250)

l [Ping Message2509](#bookmark251)  [***\*34\****](#bookmark252)

l [CHE Status 2609](#bookmark253)  [***\*35\****](#bookmark254)

l [CHE Cancel 2630](#bookmark255)  [***\*35\****](#bookmark256)

l [Surrogate CHE Selection](#bookmark257)  [***\*37\****](#bookmark258)

l [CHE Clear Message2630](#bookmark259)  [***\*38\****](#bookmark260)

l [OTR Container        ParkInventory 2709](#bookmark261)  [***\*39\****](#bookmark262)

 

 

***\*2.4.1.1 CHE\**** ***\*Log\**** ***\*In 2632\****

***\*Objective:\****

Report to ECN4 that the operator has logged into the CHE.

***\*PreCondition:\****

The current formID ofthe CHE is "FORM_LOGIN"

***\*PostCondition:\****

n CHE Status = Unavailable

n CHE JobContainerId = none

***\*Example CHE\**** ***\*Log\**** ***\*In\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps160.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps161.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps162.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps163.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps164.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps165.png)

CHE Log In - Response from ECN4 - Logged In & Unavailable for Dispatch

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps166.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps167.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps168.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps169.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps170.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps171.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps172.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps173.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps174.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps175.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps176.png)

***\*Notes\****

n Can be used to change the pool ofthe CHE, see xmlRdtRequest.xsd

 

***\******** CHE\**** ***\*Become Available 2632\****

***\*Objective:\****

Report to ECN4 that a CHE is available for executing a container transport job.

***\*PreCondition:\****

For a TT the return form is "FORM_TRUCK_UNAVAILABLE". For other types of equipment, the current formID is "FORM_UNAVAILABLE".

***\*PostCondition:\****

n CHE Status = Working

n CHE JobContainerId = none

***\*Example CHE\**** ***\*Become Available\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps177.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps178.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps179.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps180.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps181.png)

CHE Become Available - Response from ECN4 - Available for Dispatch

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps182.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps183.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps184.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps185.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps186.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps187.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps188.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps189.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps190.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps191.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps192.png)

***\*Example TT\**** ***\*Become Availab\*******\*le\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps193.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps194.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps195.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps196.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps197.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps198.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps199.png)

CHE Become Available - Response from ECN4 - Available for Dispatch

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps200.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps201.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps202.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps203.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps204.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps205.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps206.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps207.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps208.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps209.png)

If the TRAL attribute is not specified in the become available, the response is to FORM_TRUCK_TRAILER_ SELECTION

***\*Example TT\**** ***\*Become Available does\**** ***\*not specify TRAL\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps210.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps211.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps212.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps213.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps214.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps215.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps216.png)

CHE Become Available does not specify TRAL - Response from ECN4 - Trailer Selection

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps217.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps218.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps219.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps220.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps221.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps222.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps223.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps224.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps225.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps226.png)

***\*Notes\****

n When a TT becomes available, an option is present to specify that the TT is connected to a trailer.

 

***\*2.4.1.3 CHE Trailer Select\*******\*ion 2632\****

***\*Objective:\****

Report to ECN4 that a TT is available for executing a container transport job ***\*and\**** is or is not connected to a trailer.

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

***\*PreCondition:\****

The current formID is "FORM_TRUCK_TRAILER_SELECTION". In the previous transition from "FORM_TRUCK_ UNAVAILABLE", the attribute TRAL was not specified.

***\*PostCondition:\****

n CHE Status = Working

n CHE JobContainerId = none

***\*Example\**** ***\*1 TT\**** ***\*Become Available connected to\**** ***\*non-specific trailer\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps227.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps228.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps229.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps230.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps231.png)

TT Become Available - Response from ECN4 - Available for Dispatch

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps232.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps233.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps234.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps235.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps236.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps237.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps238.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps239.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps240.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps241.png)

CHE Become Available - Response from ECN4 - Did not specify TRAL attribute:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps242.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps243.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps244.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps245.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps246.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps247.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps248.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps249.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps250.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps251.png)

***\*Example 2 TT\**** ***\*Become Available conne\*******\*cted to a specific trailer\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps252.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps253.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps254.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps255.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2.4** **Example** **XMLRDT** **Mes****sages**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps256.png)

TT Become Available connected to a specific trailer - Response from ECN4 - Available for Dispatch

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps257.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps258.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps259.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps260.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps261.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps262.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps263.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps264.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps265.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps266.png)

***\*Notes\****

n By specifying connected to a trailer Y, if the next job dispatched is from a wheeled slot, the TT is directed to park the empty trailer.

n If the user specifies connected to a trailer N, and the next job dispatched is from ground, the TT is directed to pull an empty trailer.

n The TRAL attribute can be Y, N or a valid CHASSIS ID.

 

***\******** CHE\**** ***\*Become\**** ***\*Unavailable 2632\****

***\*Objective:\****

Report to ECN4 that a CHE is unavailable for executing a container transport job.

***\*PreCondition:\****

For a TT the return form is "FORM_IDLE". For other types of equipment, the current formID is "FORM_IDLE".

***\*PostCondition:\****

n CHE Status = Unavailable

***\*Example CHE\**** ***\*Become\**** ***\*Unavailable:\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps267.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps268.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps269.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps270.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps271.png)

CHE Become Unavailable - Response from ECN4 - Unavailable for Dispatch

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps272.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps273.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps274.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps275.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps276.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps277.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps278.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps279.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps280.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps281.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps282.png)

***\******** CHE\**** ***\*Log Off 2632\****

***\*Objective:\****

Report to ECN4 that a CHE is logged off.

***\*PreCondition:\****

For all types of equipment, other than TTs, the current formId is "FORM_UNAVAILABLE." For TTs, the current formId is "FORM_TRUCK_UNAVAILABLE."

***\*PostCondition:\****

n CHE Status = Unavailable

***\*Example CHE\**** ***\*Log\**** ***\*off\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps283.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps284.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps285.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps286.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps287.png)

CHE Log off - Response from ECN4

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps288.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps289.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps290.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps291.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps292.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps293.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps294.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps295.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps296.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps297.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps298.png)

 

 

***\*2.4.1.6 Area Status 2409\****

***\*Objective:\****

Request TOS to set/clear the "TEMP BLOCK" stack status state for a selected range of yard slots.

***\*PreCondition:\****

none

***\*PostCondition:\****

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

The stackstatus attribute is updated to the availability status in the response message.

***\*Example\**** ***\*1 - Area Status\**** ***\*Request (Single\**** ***\*Position)\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps299.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps300.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps301.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps302.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps303.png)

Which results in this response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps304.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps305.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps306.png)

***\*Example 2 - Area Status\**** ***\*Request (Multiple\**** ***\*Positions)\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps307.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps308.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps309.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps310.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps311.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps312.png)

Which results in this response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps313.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps314.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps315.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps316.png)

***\*Example 3 - Area Status\**** ***\*Request (Range\**** ***\*of\**** ***\*Positions)\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps317.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps318.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps319.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps320.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps321.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps322.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps323.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps324.png)

Which results in this response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps325.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps326.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps327.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps328.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps329.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps330.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

***\*Example 4 - Area Status\**** ***\*Request (Range of\**** ***\*Positions\**** ***\*-\**** ***\*Re\*******\*fID\**** ***\*Format)\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps331.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps332.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps333.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps334.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps335.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps336.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps337.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps338.png)

Which results in this response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps339.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps340.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps341.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps342.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps343.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps344.png)

***\*Notes\****

n ***\*Area status\**** ***\*position types\****: children of the ***\*<yardLocation>\**** element specify the type of action to perform:

l ***\*<position>\**** indicates a **single** yard slot affected by the area status change.

l ***\*<areaRectangle>\**** indicates a **range** of yard slots affected by the area status change.

n ***\*Area status\**** ***\*positions\**** may be expressed in **PPOS** or refID format.

n ***\*Area status stack status\**** indicates either "***\*Available\****" = **clear** 'TEMP BLOCK' stack status or "***\*Unavailab\*******\*le\****" = **set** 'TEMP BLOCK' stack status.

n Area status messages are not associated with specific CHEs. Therefore, they are not processed via form actions (such as ECN4Web forms) and do not affect the state of any CHE.

 

***\*2.4.1.7\**** ***\*Ping\**** ***\*Message 2\*******\*509\****

***\*Objective:\****

Verify that the ECN4 application is running

***\*PreCondition:\****

None

***\*PostCondition:\****

None

***\*Example\**** ***\*Ping\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps345.png)

ECN4 Response:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps346.png)

***\*Notes\****

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

n If there is no response from ECN4 then this is an error.

 

***\*2.4.1.8 CHE Status\**** ***\*2609\****

***\*Objective:\****

Have ECN4 report the current status of a CHE

***\*PreCondition:\****

None

***\*PostCondition:\****

None

***\*Example CHE\**** ***\*Status\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps347.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps348.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps349.png)

Which results in this response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps350.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps351.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps352.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps353.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps354.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps355.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps356.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps357.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps358.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps359.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps360.png)

***\*Notes\****

n Used to establish the current formId and status of the CHE. May be sent when the CHE is in any state.

 

 

***\*2.4.1.9 CHE\**** ***\*Cancel 2630\****

***\*Objective:\****

The Che wishes to cancel particular job

***\*PreCondition:\****

CHE is Dispatched to a Job or Re-handling a Container and not Laden.

***\*PostCondition:\****

None.

The CHE attribute action="X" is used to cancel the job. After issuing this command the ECN4 transitions to the previous transition in the workflow based on the current state and defined state model.

***\*Example\**** ***\*1 Cancel while CHE\**** ***\*is dispatche\*******\*d\**** ***\*a job\****

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

Current formId

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps361.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps362.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps363.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps364.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps365.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps366.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps367.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps368.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps369.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps370.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps371.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps372.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps373.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps374.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps375.png)

Request to cancel the dispatched job

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps376.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps377.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps378.png)

ECN4 transitions back to job list as a result of cancel to the previous form: FORM_IDLE or FORM_JOB_LIST.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps379.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps380.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps381.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps382.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps383.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps384.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps385.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps386.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps387.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps388.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps389.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps390.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps391.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps392.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps393.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps394.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps395.png)

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps396.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps397.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps398.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps399.png)

[***\*2.4.1.10\****](2.4.1.10) ***\*Surrogate CHE\**** ***\*Selection\****

***\*Objective:\****

Report to ECN4 that the operator is login as Surrogate CHE. ***\*PreCondition:\****

EC User Login

***\*PostCondition:\****

User is logged in as a surrogate of the selected CHE.

***\*Example CHE\**** ***\*Login\**** ***\*Request\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps400.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps401.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps402.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps403.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps404.png)

CHE Log In - Response from ECN4 to Surrogate CHE Selection:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps405.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps406.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps407.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps408.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps409.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps410.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps411.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps412.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps413.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps414.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps415.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps416.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps417.png)

Request to Surrogate the CHE, in this example HCG01 is surrogate for 'MA3':

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps418.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps419.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps420.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps421.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps422.png)

Response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps423.png)

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps424.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps425.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps426.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps427.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps428.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps429.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps430.png)

***\*Notes\****

n Once the surrogate CHE has logged in there is an attribute note SCHE="HCG01" and ACHE="MA3" attributes as shown in the above FORM_UNAVAILABLE message.

 

[***\*2.4.1.11\****](2.4.1.11) ***\*CHE Clear\**** ***\*Message 2630\****

***\*Objective:\****

Users can send text messages to CHE drivers and this message appears on the VMT screen. The driver wants to dis- miss the message.

***\*PreCondition:\****

The current XMLRDT message has a text message displayed in the displayMsg node.

***\*PostCondition:\****

The XMLRDT message no longer has the text message displayed in the displayMsg node.

Example: Clear Message Previous message:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps431.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps432.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps433.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps434.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps435.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps436.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps437.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps438.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps439.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps440.png)

 <container EQID="TEST0000003" LNTH="20" QWGT="30000" MNRS="" QCAT="E" EQTP="2000"   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps441.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps442.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps443.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps444.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps445.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps446.png)

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps447.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps448.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps449.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps450.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps451.png)

Clear Message request:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps452.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps453.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps454.png)

Which results in this response from ECN4, the same XMLRDT as before, but with the displayMsg cleared:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps455.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps456.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps457.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps458.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps459.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps460.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps461.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps462.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps463.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps464.png)

 <container EQID="TEST0000003" LNTH="20" QWGT="30000" MNRS="" QCAT="E" EQTP="2000"   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps465.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps466.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps467.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps468.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps469.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps470.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps471.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps472.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps473.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps474.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps475.png)

[***\*2.4.1.12\****](2.4.1.12) ***\*OTR Container Inventory\**** ***\*Park 2709\****

***\*Objective:\****

Report ECN4 that OTR attempted a park to inventory a container in the yard

***\*PreCondition:\****

Since this message is for a non-che specific operation, there is no formID associated. The follow conditions must be true for this message:

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

n Valid truck License - TRKL

n Valid yard PPOS validation - PPOS

n Valid wheeled Slot

n Truck License associated Receival WorkInstruction

n Maximum 6 Parking per attempt

n Restriction of the 40ft Container parking

***\*PostCondition:\****

Container on chassis attached to TRKL license is inventoried to position PPOS

***\*Example\**** ***\*1: Successful OTR Container Inventory\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps476.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps477.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps478.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps479.png)

Response from ECN4 - acknowledged with msgID="0" meaning No Error.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps480.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps481.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps482.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps483.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps484.png)

***\*Example 2: OTR\**** ***\*Park\**** ***\*Failures\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps485.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps486.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps487.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps488.png)

Response from ECN4 - failure notification XXX is not a valid position

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps489.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps490.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps491.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps492.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps493.png)

***\*Example 3: OTR\**** ***\*parks\**** ***\*in a\**** ***\*no\*******\*n-wheeled slot\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps494.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps495.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps496.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps497.png)

Response from ECN4 - failure notification CPA is an invalid wheeled position

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps498.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps499.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps500.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps501.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps502.png)

***\*Example 4: OTR does\**** ***\*not\**** ***\*have a valid\**** ***\*receival work\**** ***\*inst\*******\*ruction\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps503.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps504.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps505.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps506.png)

Response from ECN4 - failure notification truck license LIC1234 does not have a valid work instruction

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps507.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps508.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps509.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps510.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps511.png)

***\*Example 5: OTR\**** ***\*parks\**** ***\*i\*******\*n an\**** ***\*occupied\**** ***\*slot\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps512.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps513.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps514.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps515.png)

Response from ECN4 - failure notification that position is occupied by another container

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps516.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps517.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps518.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps519.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps520.png)

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

***\*2.4.2\**** ***\*Example\**** ***\*CHE\**** ***\*Messages\****

l [CHE Lift](#bookmark263)  [***\*42\****](#bookmark264)

l [CHE Confirm Container 2631](#bookmark265)  [***\*50\****](#bookmark266)

l [CHE Set](#bookmark267)  [***\*52\****](#bookmark268)

l [CHE Auto Rehandle](#bookmark269)  [***\*56\****](#bookmark270)

l [CHE Manual Rehandle 2630](#bookmark271)  [***\*57\****](#bookmark272)

l [CHE Progress Update](#bookmark273)  [***\*59\****](#bookmark274)

l [CHE Joblist Request 2724](#bookmark275)  [***\*60\****](#bookmark276)

l [CHE Joblist Select 2630](#bookmark277)  [***\*64\****](#bookmark278)

l [CHE Truck Departure2630](#bookmark279)  [***\*66\****](#bookmark280)

l [CHE Joblist Promotion 2535](#bookmark281)  [***\*67\****](#bookmark282)

l [CHE Show Joblist Filter 2727](#bookmark283)  [***\*67\****](#bookmark284)

l [CHE Show Joblist Sort 2727](#bookmark285)  [***\*68\****](#bookmark286)

l [CHE Select Empty Delivery 2630](#bookmark287)  [***\*69\****](#bookmark288)

l [CHE Empty to Origin](#bookmark289)  [***\*70\****](#bookmark290)

l [CHE TBD Merge](#bookmark291)  [***\*71\****](#bookmark292)

l [CHE Dispatch TT](#bookmark293)  [***\*73\****](#bookmark294)

l [CHE Search Truck Jobs](#bookmark295)  [***\*74\****](#bookmark296)

l [CHE Swap Position](#bookmark297)  [***\*74\****](#bookmark298)

l [CHE ChangeListMode 2727](#bookmark299)  [***\*76\****](#bookmark300)

l [CHE Self-Assign2640](#bookmark301)  [***\*79\****](#bookmark302)

 

 

***\*2.4.2.1 CHE\**** ***\*Lift\****

***\*Objective:\****

Report to ECN4 that a CHE has lifted a container. Causes ECN4 to advance the CHE to the next part of the job or show job as lifted.

***\*PreCondition:\****

The CHE has been dispatched a job.

***\*PostCondition:\****

If the CHE is in "self-complete" operating mode, then the job move stage is advanced to CARRY UNDERWAY. The response message will result in a new form to the CHE, but the formId will depend on the workflow and lift message.

***\*Example\**** ***\*1 CHE\**** ***\*Lift\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps521.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps522.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps523.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps524.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps525.png)

If the system sending the Lift message can identify the container EQID, then this can be sent instead of the position. When the container EQID is sent, then the container will be found (if it exists in the system) and FORM_CONFIRM_ CONTAINER will not be prompted to the user.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps526.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps527.png)

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps528.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps529.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps530.png)

Response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps531.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps532.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps533.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps534.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps535.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps536.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps537.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps538.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps539.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps540.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps541.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps542.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps543.png)

 <job MVKD="YARD" pow="UNASSIGNED" age="9" priority="N" shift="0" ingress="HIGH">   <container EQID="LOAD0000004" LNTH="20" QWGT="3000" EQTP="2000" HGHT="2590"   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps544.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps545.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps546.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps547.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps548.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps549.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps550.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps551.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps552.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps553.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps554.png)

When container is lifted when CHE is in job list mode the return form is FORM_DISPATCH_LIFTED. CHEs operating in job list mode are often in "require handler" operating mode, so upon lift the move stage of the job does not advance to  CARRY UNDERWAY, but rather stays PLANNED.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps555.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps556.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps557.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps558.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps559.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps560.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps561.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps562.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps563.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps564.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps565.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps566.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps567.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps568.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps569.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps570.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps571.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps572.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps573.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps574.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps575.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps576.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps577.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps578.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps579.png)

The CHE operator is lifting a container, but the position sent for the lift cannot resolve to a single container which results a confirm container :

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps580.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps581.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps582.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps583.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps584.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps585.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps586.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps587.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps588.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps589.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps590.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps591.png)

When che operator selects a TBDUnit and lifted container for merge but ECN4 cannot determine lifted container :

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps592.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps593.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps594.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps595.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps596.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps597.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps598.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps599.png)

When CHE is rehandling a container in a way of target:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps600.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps601.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps602.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps603.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps604.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps605.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps606.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps607.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps608.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps609.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps610.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps611.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps612.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps613.png)

 <job MVKD="SHFT" pow="" age="0" priority="N" shift="0" target="TEST0000101">     

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps614.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps615.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps616.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps617.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps618.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps619.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps620.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps621.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps622.png)

This xml response is applicable to following REHANDLE variations

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps623.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps624.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps625.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps626.png)

CHE Lift - Error Response:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps627.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps628.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps629.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps630.png)

 <container EQID="LOAD0000004" LNTH="20" QWGT="3000" EQTP="2000" HGHT="2590"   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps631.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps632.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps633.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps634.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps635.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps636.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps637.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps638.png)

 <displayMsg msgID="112">Unable to lift container from >Position PPOS='338C15.A'<  

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps639.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps640.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps641.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps642.png)

***\*Example 2 CHE\**** ***\*Lift a 20' container in\**** ***\*the\**** ***\*front\**** ***\*position\**** ***\*of the\**** ***\*spreader\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps643.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps644.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps645.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps646.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps647.png)

***\*Example 3 CHE\**** ***\*Lift 2\**** ***\*20'\**** ***\*con\*******\*tainers\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps648.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps649.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps650.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps651.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps652.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps653.png)

***\*Example 4 CHE\**** ***\*Lift Supplyi\*******\*ng gross weight\**** ***\*information, for more\**** ***\*details\**** ***\*on this\**** ***\*see\**** ***\*Container Weight\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps654.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps655.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps656.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps657.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps658.png)

***\*Example 5 CHE\**** ***\*Lift from\**** ***\*t\*******\*ruck\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps659.png)

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps660.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps661.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps662.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps663.png)

***\*Example 6 CHE does a\**** ***\*partial twin\**** ***\*lift\**** ***\*notice SPOS="T"\**** ***\*and\**** ***\*there\**** ***\*is\**** ***\*only\**** ***\*one\**** ***\*container\**** ***\*position\**** ***\*in\**** ***\*lift\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps664.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps665.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps666.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps667.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps668.png)

Response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps669.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps670.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps671.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps672.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps673.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps674.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps675.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps676.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps677.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps678.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps679.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps680.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps681.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps682.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps683.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps684.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps685.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps686.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps687.png)

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps688.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps689.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps690.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps691.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps692.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps693.png)

***\*Notes\****

n The 2635 message supports Lat/Long Telemetry based lift messages. There is a precedence order used to determine what container has been lifted:

l container EQID

l PPOS

l refID

l geodetic

It should be understood that if the XMLRDT Client sends a for the PPOS attribute then Lat/Long Geometry is

ignored completely. If the position attribute is present, but the PPOS="", ECN4 will return error 112 Unable to lift container, unless the position is being conveyed by ***\*geodetic\****  or ***\*refId\**** positions, as the above precedence order requires PPOS to be an empty string for a refId or geodetic position to be used. The PPOS attribute must be an  empty string because PPOS is a required field on the position node. If the position node is missing, ENC4 will

optimistically assume the container which was planned to be lifted was lifted. If the container EQID is supplied along with a PPOS, the EQID takes precedence.

n The spreader position attribute (SPOS) values are summarized below:

 

| ***\*SPOS Attribute\**** | ***\*Spreader Position\**** |
| ------------------------ | --------------------------- |
| SPOS='T'                 | Twin position               |
| SPOS='2'                 | 20' position                |
| SPOS='4'                 | 40' position                |
| SPOS='5'                 | 45' position                |

n There are three potential values for Job Position (JPOS): FWD, CTR, AFT. When containers are being single   carried it is expected the JPOS is 'CTR' and 'FWD' and 'AFT' are used to determine where containers being twin lifted are located on the spreader.

n 4. Other Examples:

Lift message (single):

<message type="2630" MSID="6" formId="FORM_DISPATCH">

<che CHID="51" action="L" SPOS="4">

<position PPOS="M27F05.B" JPOS="CTR"/>

</che>

</message>

Lift message (twin) from yard:

<message type="2630" MSID="6" formId="FORM_DISPATCH">

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

<che CHID="51" action="L" SPOS="T">

<position PPOS="M27F05.B" JPOS="FWD"/>

<position PPOS="M27F03.B" JPOS="AFT"/>

</che>

</message>

Lift message (twin) from truck:

<message type="2630" MSID="6" formId="FORM_DISPATCH">

<che CHID="51" action="L" SPOS="T">

<position PPOS="NO_SLOT" CHID="T101" TKPS="1" JPOS="FWD" />

<position PPOS="NO_SLOT" CHID="T101" TKPS="2" JPOS="AFT" />

</che>

</message>

Lift Message with Lat/Long and Telemetry

<message type="2635" MSID="6" eventTime="2010-08-10T12:02:38.632+02:00">

<che CHID="51" action="Lift" SPOS="4">

<position PPOS=" " JPOS="CTR">

<geodeticData Latitude="48.1234" Longitude="128.3342" Height="260" Head-

ing="245.5"/>

</position> </che>

</message>

Twin Lift Message with Lat/Long and Telemetry

<message type="2635" MSID="6" eventTime="2010-08-10T12:02:38.632+02:00">

<che CHID="51" action="Lift" SPOS="T">

<position PPOS=" " JPOS="FWD">

<geodeticData Latitude="48.1245" Longitude="128.3542" Height="260" Head-

ing="245.5"/>

</position>

<position PPOS=" " JPOS="AFT">

<geodeticData Latitude="48.1234" Longitude="128.3342" Height="260" Head-

ing="245.5"/>

</position> </che>

</message>

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

Lift Message from Yard Tractor

<message type="2635" MSID="16" eventTime="2010-08-10T12:02:38.632+02:00">

<che CHID="MA3" action="Lift">

<position PPOS=" " JPOS="CTR" CHID="1831" TKPS="1">

<geodeticData Latitude="48.1234" Longitude="128.3342" Height="260" Head-

ing="245.5"/>

</position>

</che>

</message>

 

***\*2.4.2.2 CHE Confirm C\*******\*ontainer\**** ***\*2631\****

***\*Objective:\****

Report to ECN4 that the CHE operator has Confirmed Lifted Container.

***\*PreCondition:\****

The Lift operation at Quay or by PDS cannot resolve a container

***\*PostCondition:\****

n CHE state is now lifted with confirmed container. This may put the CHE on FORM_EMPTY_AT_ORIGIN, FORM_REHANDLE, or FORM_DISPATCH_LIFTED.

***\*Example\**** ***\*1 CHE Confirm Container when\**** ***\*CHE\**** ***\*is\**** ***\*on\**** ***\*a job\**** ***\*list\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps694.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps695.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps696.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps697.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps698.png)

CHE Confirm Container - Response from ECN4

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps699.png)

<che CHID="701" equipType="STRAD" twinCapable="N" status="Working" locale="en_US">   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps700.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps701.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps702.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps703.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps704.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps705.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps706.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps707.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps708.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps709.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps710.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps711.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps712.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps713.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps714.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps715.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps716.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps717.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps718.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps719.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps720.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps721.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps722.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps723.png)

***\*Example 2 CHE Confirm Container\**** ***\*when Straddle\**** ***\*is job stepping\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps724.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps725.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps726.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps727.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps728.png)

CHE Confirm Container - Response from ECN4

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps729.png)

<che CHID="720" equipType="STRAD" twinCapable="N" status="Working" locale="en_US">   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps730.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps731.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps732.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps733.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps734.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps735.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps736.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps737.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps738.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps739.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps740.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps741.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps742.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps743.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps744.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps745.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps746.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps747.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps748.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps749.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps750.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps751.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps752.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps753.png)

Latitude='128.3342' Longitude='48.1234' Height='260' Heading='245.5'

***\*2.4.2.3 CHE\**** ***\*Set\****

***\*Objective:\****

Report to ECN4 that the straddle carrier has set down a container. Causes ECN4 to complete the job. ***\*PreCondition:\****

The CHE is carrying a container

***\*PostCondition:\****

The CHE is IDLE, barring FORM_CONFIRM_TRUCK conditions and no longer carrying the container

***\*Example\**** ***\*1 CHE\**** ***\*Set\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps754.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps755.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps756.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps757.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps758.png)

Which results in this response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps759.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps760.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps761.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps762.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps763.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps764.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps765.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps766.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps767.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps768.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps769.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps770.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps771.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps772.png)

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps773.png)

Example Error Condition Response from ECN4

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps774.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps775.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps776.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps777.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps778.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps779.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps780.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps781.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps782.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps783.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps784.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps785.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps786.png)

 <job MVKD="YARD" pow="UNASSIGNED" age="0" priority="N" shift="0" ingress="HIGH">   <container EQID="LOAD0000004" LNTH="20" QWGT="3000" EQTP="2000" HGHT="2590"   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps787.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps788.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps789.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps790.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps791.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps792.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps793.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps794.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps795.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps796.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps797.png)

***\*Example 2 CHE\**** ***\*Set\**** ***\*on\**** ***\*truck\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps798.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps799.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps800.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps801.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps802.png)

When ECN4 is unable to determine the Truck during "Set" then it transitions to CONFIRM_TRUCK

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps803.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps804.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps805.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps806.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2.4** **Example** **XMLRDT** **Mes****sages**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps807.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps808.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps809.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps810.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps811.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps812.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps813.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps814.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps815.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps816.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps817.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps818.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps819.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps820.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps821.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps822.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps823.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps824.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps825.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps826.png)

***\*Example 3 CHE Set onto\**** ***\*unattached\**** ***\*chassis\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps827.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps828.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps829.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps830.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps831.png)

***\*Notes\****

n If the position node is missing, ENC4 will not assume the container has been set and instead returns 101 No pos- ition specified

n If the position attribute is present, but the PPOS="", ECN4 will return error 101 No position specified

n If the TKPS or JPOS attribute is not present, then ECN4 drops the container on the truck based on the value set in the EC parameters TRKSSQ and TRKDIR.

n Similar to theChe Lift, the 2635 CHE Set message supports Lat/Long Telemetry based lift messages. It is also important to note that if the PDS system pass value in PPOS attribute then Lat/Long Geometry is ignored com- pletely.

***\*Other Examples\****

Set message (single):

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps832.png)

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps833.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps834.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps835.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps836.png)

Set message specifying container:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps837.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps838.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps839.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps840.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps841.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps842.png)

Some external systems provide the EQID on set, to be consistent with the PDS lift message. This is allowed by the schema, but the EQID is ignored by ECN4.

Set message (twin): to yard

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps843.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps844.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps845.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps846.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps847.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps848.png)

Set message (twin): to truck

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps849.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps850.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps851.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps852.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps853.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps854.png)

For rail load performed by XMLRDT, if the PDS system can specify a rail car slot then the position supplied will be respected:

Set message: to rail car

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps855.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps856.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps857.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps858.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps859.png)

Geodetic:

Set Message with Lat/Long and Telemetry

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps860.png)

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps861.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps862.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps863.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps864.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps865.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps866.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps867.png)

Twin Set Message with Lat/Long and Telemetry

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps868.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps869.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps870.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps871.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps872.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps873.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps874.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps875.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps876.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps877.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps878.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps879.png)

Set Message to Yard Tractor

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps880.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps881.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps882.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps883.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps884.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps885.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps886.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps887.png)

***\*2.4.2.4 CHE Auto\**** ***\*Reh\*******\*andle\****

***\*Objective:\****

Signal from ECN4 that the CHE must complete a rehandle before it can complete its target move.

***\*PreCondition:\****

The CHE is about to perform a job that requires moving a container to access the target container for the job. The CHE may be on FORM_EMPTY_TO_ORIGIN or FORM_JOB_LIST.

***\*PostCondition:\****

CHE Status = Rehandling

***\*Example CHE Auto\**** ***\*Rehan\*******\*dle\**** ***\*Message\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps888.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps889.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps890.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps891.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps892.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps893.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps894.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps895.png)

 <job MVKD="SHFT" pow="" age="0" priority="N" shift="0" target="TEST0000002">     

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps896.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps897.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps898.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps899.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps900.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps901.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps902.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps903.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps904.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps905.png)

The expected request sent by the XMLRDT Client after the FORM_AUTO_REHANDLE has been pushed is a container set to the suggested position.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps906.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps907.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps908.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps909.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps910.png)

***\*Notes\****

n This message is pushed by ECN4 when the CHE is required to move a container that is not the dispatched job.

 

***\*2.4.2.5 CHE\**** ***\*Manual\**** ***\*Rehandle 2630\****

***\*Objective:\****

Signal that the CHE wishes to rehandle or inventory a container

***\*PreCondition:\****

None

***\*PostCondition:\****

None

***\*Example\**** ***\*1\**** ***\*Manually\**** ***\*Rehandle\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps911.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps912.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps913.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2.4** **Example** **XMLRDT** **Mes****sages**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps914.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps915.png)

Which results in this response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps916.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps917.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps918.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps919.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps920.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps921.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps922.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps923.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps924.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps925.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps926.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps927.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps928.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps929.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps930.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps931.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps932.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps933.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps934.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps935.png)

To which the XMLRDT Client should respond with:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps936.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps937.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps938.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps939.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps940.png)

***\*Example 2\**** ***\*Manual\**** ***\*Rehandle One\**** ***\*Message\****

Example 1 used two different messages to indicate a rehandle. This example shows how to indicate a rehandle with

only one message. This is only supported in 3.1 and from FORM_IDLE or FORM_JOB_LIST. In this message the TKPS is specifying which position on the chassis located in position KCS02.BT1 this container is being placed and which way  the doors are facing.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps941.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps942.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps943.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps944.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps945.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps946.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

***\*2.4.2.6 CHE\**** ***\*Progress\**** ***\*Update\****

***\*Objective:\****

Report to ECN4 that the straddle carrier has completed a particular part of the job. Causes ECN4 to advance the CHE to the next part of the job when the CHE is job stepping. If the CHE is not job stepping, then this will just update the

CHE's last known position.

***\*PreCondition:\****

***\*PostCondition:\****

CHE's last known position is updated.

***\*Example CHE\**** ***\*Progress\**** ***\*Update\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps947.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps948.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps949.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps950.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps951.png)

CHE Progress Update - Response from ECN4 - formID has been advanced

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps952.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps953.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps954.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps955.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps956.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps957.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps958.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps959.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps960.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps961.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps962.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps963.png)

 <job MVKD="YARD" pow="UNASSIGNED" age="23" priority="N" shift="0" ingress="HIGH">  

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps964.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps965.png)

 <position PPOS="338C18.A" AREA="338C" AREA_TYPE="YardRow" type="from" DOOR="?" /> 

 <position PPOS="338C15.A" AREA="338C" AREA_TYPE="YardRow" type="to" DOOR="?" />   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps966.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps967.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps968.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps969.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps970.png)

CHE Progress Update - Response from ECN4 - Error case.

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

<message formId="FORM_EMPTY_TO_ORIGIN" ack="Y" MSID="2">                 <che CHID="701" equipType="STRAD" status="Unavail">                   

 <pool name="UNASSIGNED">                               

 <list count="6" type="pow">                              

 <pow name="K32" mode="Manual" />                           

 <pow name="K33" mode="Manual" />                           

 <pow name="K34" mode="Manual" />                           

 <pow name="K35" mode="Manual" />                           

 <pow name="K36" mode="Manual" />                           

 <pow name="UNASSIGNED" mode="OptimizeStrads" />                   

 </list>                                        

 </pool>                                        

 <displayMsg msgID="200">Unknown user 'asdasd'</displayMsg>               </che>                                         

</message>                                        

***\*Notes\****

n In the above example, the CHE was FORM_EMPTY_TO_ORIGIN. The CHE operator signaled that the CHE was now at 338C. Hence the form changed to be FORM_EMPTY_AT_ORIGIN.

n The position can be a full slot position that includes the tier (338C18.A), a row level position which corresponds to the AREA attribute (338C), or a POW (K31-0).

***\*Other Examples\****

Position message at a fully slotted position

<message type="2635" MSID="6">                              

<che CHID="51" action="ProgressUpdate">                         

  <position PPOS="M27F05.B"/>                              </che>                                          

</message>                                        

Position message at POW K31

<message type='2630' formId='FORM_EMPTY_TO_ORIGIN' MSID='8'>               <che CHID='702' action='B'>                               

  <position PPOS='K31'/>                                 </che>                                          

</message>                                        

***\*2.4.2.7 CHE Joblist\**** ***\*Request 2724\****

***\*Objective:\****

Request a list of jobs which the CHE may select

***\*PreCondition:\****

The CHE is on FORM_IDLE or FORM_JOB_LIST.

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

***\*PostCondition:\****

The CHE is on FORM_JOB_LIST.

***\*Example\**** ***\*1 CHE Joblist\**** ***\*Request a job\**** ***\*list of si\*******\*ze six\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps971.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps972.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps973.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps974.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps975.png)

Response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps976.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps977.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps978.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps979.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps980.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps981.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps982.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps983.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps984.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps985.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps986.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps987.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps988.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps989.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps990.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps991.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps992.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps993.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps994.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps995.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps996.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps997.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps998.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps999.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1000.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1001.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1002.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1003.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1004.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1005.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1006.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1007.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1008.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1009.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1010.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1011.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1012.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1013.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1014.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1015.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1016.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1017.png)

***\*Example 2 CHE Joblist\**** ***\*Request Size\****: 2jobs beginning with (but not including) container RAIL0000104

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1018.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1019.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1020.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1021.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1022.png)

Response 2 from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1023.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1024.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1025.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1026.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1027.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1028.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1029.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1030.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1031.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1032.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1033.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1034.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1035.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1036.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1037.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1038.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1039.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1040.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1041.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1042.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1043.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1044.png)

***\*Example 3 CHE Joblist request the single\**** ***\*job\**** ***\*prior to specific job\**** RAIL0000104

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1045.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1046.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1047.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1048.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1049.png)

Response from ECN4

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1050.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1051.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1052.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1053.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1054.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1055.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1056.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1057.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1058.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1059.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1060.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1061.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1062.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1063.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1064.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1065.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1066.png)

***\*Example 4 CHE Joblist request\**** ***\*for TBD\**** ***\*unit\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1067.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1068.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1069.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1070.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1071.png)

Response from ECN4 containing the TBD unit

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1072.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1073.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1074.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1075.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1076.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1077.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1078.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1079.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1080.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1081.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1082.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1083.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1084.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1085.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1086.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1087.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1088.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1089.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1090.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1091.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1092.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1093.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1094.png)

***\*Notes\****

n In Zone coverage for a job to be a candidate for a CHE, the CHE must be covering the start position. The job must also be marked for fetch.

n A joblist size of "-0" means return all the jobs prior to the current one.

 

***\*2.4.2.8 CHE Joblist Select\**** ***\*2630\****

***\*Objective:\****

Signal that the CHE is assigned a particular job.

***\*PreCondition:\****

CHE is on the FORM_IDlLE or FORM_JOB_LIST.

***\*PostCondition:\****

CHE is dispatched a job.

***\*Example\**** ***\*1 Job Select\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1095.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1096.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1097.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1098.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1099.png)

Which results in the following from ECN4

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1100.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1101.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1102.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1103.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1104.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1105.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1106.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1107.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1108.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1109.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1110.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1111.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1112.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1113.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1114.png)

***\*Example 2 Select a Container but\**** ***\*do\**** ***\*not\**** ***\*provide a\**** ***\*uni\*******\*que\**** ***\*container\**** ***\*ID\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1115.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1116.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1117.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1118.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1119.png)

ECN4 resend the previous form with a message with an error message to the user:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1120.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1121.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1122.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1123.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1124.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1125.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1126.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1127.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1128.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1129.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1130.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1131.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1132.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1133.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1134.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1135.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1136.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1137.png)

 <displayMsg msgID="204">More than one container matching '501'</displayMsg>     

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1138.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1139.png)

***\*Example 3 Select a TBD\**** ***\*unit job from the job\**** ***\*list\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1140.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1141.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1142.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1143.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1144.png)

Response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1145.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1146.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1147.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1148.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1149.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1150.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1151.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1152.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1153.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1154.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1155.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1156.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1157.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1158.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1159.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1160.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1161.png)

***\*2.4.2.9 CHE Truck\**** ***\*Departure 2630\****

***\*Objective:\****

ECN4 creates an EC event ***\*EXLN,\**** when the truck is departed from the quay crane (QC) lane.

***\*PreCondition:\****

\1.  The QC name in the ***\*PPOS\**** attribute must be a valid QC; otherwise, the EC event EXLN is not created. PPOS format: QC name - Lane - Tier; Example: QC21-1-1.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1162.png)

\2.  If the message contains an ***\*eventTime\**** attribute, the EC event creation time is the value indicated in the attrib- ute. If this attribute is not used, the time when the message was posted to ECN4 is treated as the EC event cre- ation time. ***\*Event time fo\*******\*rmat: yyyy-MM-dd'T'HH:mm:ss.\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1163.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

\3.  The QC lane is not checked, presuming that the RTLS system transmits the correct QC lane data.

\4.  Message is assumed to be posted to ECN4 once the truck departs the QC lane. ECN4 will not confirm whether the terminal truck left the lane.

***\*PostCondition:\****

ECN4 creates ***\*EXLN\**** EC event record.

***\*Example\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1164.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1165.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1166.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1167.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1168.png)

[***\*2.4.2.10\****](2.4.2.10) ***\*CHE Joblist\**** ***\*Promotion 2535\****

***\*Objective:\****

Signal that the Che wishes to promote a particular job

***\*PreCondition:\****

CHE is logged in and available

***\*PostCondition:\****

None

***\*Example\**** ***\*1\**** ***\*Promote a job for an\**** ***\*External Truc\*******\*k (OTR)\****

Here the CHE R17 will have the truck job for license 9E14267 promoted to the top of its job list.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1169.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1170.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1171.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1172.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1173.png)

In the above message the truck license is given to identify the truck.

***\*Example 2\**** ***\*Promote a job for a TT\****

Here the CHE R17 will have the truck job for internal CHE id TT1705 promoted to the top of its job list.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1174.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1175.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1176.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1177.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1178.png)

In the above message the short name of the CHE is given to identify the TT.

[***\*2.4.2.11\****](2.4.2.11) ***\*CHE Show Joblist\**** ***\*Filter 2727\****

***\*Objective:\****

Signal that the forklift wishes to show a job list filter extension list

***\*PreCondition:\****

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

CHE is on the FORM_JOB_LIST form and has a valid list of jobs.

***\*PostCondition:\****

None

***\*Example\**** ***\*1\**** ***\*Request a JobList\**** ***\*Filter\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1179.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1180.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1181.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1182.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1183.png)

Which results in the following from ECN4

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1184.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1185.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1186.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1187.png)

 <option index="2" name="Mock Joblist Filter A" label="Mock Joblist Filter A"/>  

 <option index="3" name="Mock Joblist Filter B" label="Mock Joblist Filter B"/>  

 <option index="4" name="Mock Joblist Filter C" label="Mock Joblist Filter C"/>  

 <option index="5" name="Mock Joblist Filter D" label="Mock Joblist Filter D"/>  

 <option index="6" name="Mock Joblist Filter E" label="Mock Joblist Filter E"/>  

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1188.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1189.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1190.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1191.png)

Once on this form a selection response looks like below, where the name of the filter is "Mock Joblist Filter A" and no parameters passed:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1192.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1193.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1194.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1195.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1196.png)

ECN4 will send FORM_JOB_LIST with the selected filter applied.

[***\*2.4.2.12\****](2.4.2.12) ***\*CHE Show J\*******\*oblist Sort\**** ***\*2727\****

***\*Objective:\****

Signal that the forklift wishes to show a job list sort extension list

***\*PreCondition:\****

CHE is on the FORM_JOB_LIST form and has a valid list of jobs.

***\*PostCondition:\****

None

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

***\*Example\**** ***\*1\**** ***\*Request a JobLis\*******\*t Sorter\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1197.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1198.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1199.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1200.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1201.png)

Which results in the following from ECN4

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1202.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1203.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1204.png)

 <option index="1" name="Mock Joblist Sorter A" label="Mock Joblist Sorter A"/>   

 <option index="2" name="Mock Joblist Sorter B" label="Mock Joblist Sorter B"/>   

 <option index="3" name="Mock Joblist Sorter C" label="Mock Joblist Sorter C"/>   

 <option index="4" name="Mock Joblist Sorter D" label="Mock Joblist Sorter D"/>   

 <option index="5" name="Mock Joblist Sorter E" label="Mock Joblist Sorter E"/>   

 <option index="6" name="Mock Joblist Sorter F" label="Mock Joblist Sorter F"/>   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1205.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1206.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1207.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1208.png)

Once on this form a selection response looks like below, where the name of the sort is "Mock Joblist Sorter A" and no parameters passed:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1209.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1210.png)

<joblist fromEQID="" size="6" sortType="Mock Joblist Sorter A" sortUserParameter=""/>  

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1211.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1212.png)

ECN4 will send FORM_JOB_LIST with the selected sort applied.

[***\*2.4.2.13\****](2.4.2.13) ***\*CHE Select\**** ***\*Empty\**** ***\*D\*******\*elivery 2630\****

***\*Objective:\****

Signal that the CHE wishes to select a particular empty delivery job. ***\*PreCondition:\****

CHE is on the FORM_JOB_LIST form and has a valid list of jobs. ***\*PostCondition:\****

None

***\*Example\**** ***\*1 Select an\**** ***\*Empty\**** ***\*Delivery\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1213.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1214.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1215.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1216.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1217.png)

Which results in the following from ECN4, note the swappableDelivery attribute on the job.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1218.png)

<che CHID="720" equipType="FORK" MTEU="2" DSTA="IDLE" OPMD="SELF_COMPLETE" APOW="K31" status="Working" locale="en_US" userID="1234" range="AG 332>>329 3>>6">   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1219.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1220.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1221.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1222.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1223.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1224.png)

 <job MVKD="DLVR" pow="UNASSIGNED" swappableDelivery="Y" age="4063145" priority="N"  

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1225.png)

 <container EQID="TEST0000001" LNTH="20" QWGT="30000" MNRS="" QCAT="E" EQTP="2000"   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1226.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1227.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1228.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1229.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1230.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1231.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1232.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1233.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1234.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1235.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1236.png)

[***\*********\****](********) ***\*CHE\**** ***\*Empty to Orig\*******\*in\****

***\*Objective:\****

Signal from ECN4 that the CHE has been dispatched a job. Unlike all the other XMLRDT messages defined in this SDK, this message is emitted from ECN4 without any external input. This message is driven by PR-SC dispatching the

straddle carrier.

***\*PreCondition:\****

For a straddle carrier, the current formID is "FORM_IDLE".

***\*PostCondition:\****

n CHE Status = working

n CHE JobContainerId

For descriptions of the elements and attributes refer to the xmlRdtResponse.xsd and the xmlRdtRequest.xsd. Example CHE Empty to Origin

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1237.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1238.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1239.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1240.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1241.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1242.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1243.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1244.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1245.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1246.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1247.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1248.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1249.png)

 <job MVKD="YARD" pow="UNASSIGNED" age="0" priority="N" shift="0" ingress="HIGH">   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1250.png)

 <position PPOS="338C18.A" AREA="338C" AREA_TYPE="YardRow" type="from" DOOR="?" /> 

 <position PPOS="338C15.A" AREA="338C" AREA_TYPE="YardRow" type="to" DOOR="?" />   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1251.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1252.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1253.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1254.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1255.png)

***\*Notes\****

n Sent by ECN4, because of this, no message ID is present in the message.

 

[***\*2.4.2.15\****](2.4.2.15) ***\*CHE TBD\**** ***\*Merge\****

***\*Objective:\****

A CHE is performing a TBD Job and wants to merge a container to the empty job.

***\*PreCondition:\****

The current formId is FORM_TBDUNIT or FORM_EMPTY_AT_ORIGIN while performing a TBD job. ***\*PostCondition:\****

CHE is dispatched a container job.

***\*Example\**** ***\*Merge\**** ***\*Message:\****

Previous response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1256.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1257.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1258.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1259.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1260.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1261.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1262.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1263.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1264.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1265.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1266.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1267.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1268.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1269.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1270.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1271.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1272.png)

Merge Request to ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1273.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1274.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1275.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1276.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1277.png)

Response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1278.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1279.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1280.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1281.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1282.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1283.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1284.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1285.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1286.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1287.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1288.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1289.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1290.png)

 <job MVKD="LOAD" pow="UNASSIGNED" age="someMinutes" priority="N" shift="0">    

 <container EQID="TESTLOAD003" LNTH="40" QWGT="1000" EQTP="" HGHT="2591"     

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1291.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1292.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1293.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1294.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1295.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1296.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1297.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1298.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1299.png)

[***\*2.4.2.16\****](2.4.2.16) ***\*CHE\**** ***\*Dispatch TT\****

***\*Objective:\****

Request a job to be dispatched to a TT.

***\*PreCondition:\****

The current formId is FORM_DISPATCH. There is an IDLE and available TT for dispatch. ***\*PostCondition:\****

CHE Status = LoggedOn

***\*Example CHE\**** ***\*Dispatch TT\****

Previous response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1300.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1301.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1302.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1303.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1304.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1305.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1306.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1307.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1308.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1309.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1310.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1311.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1312.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1313.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1314.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1315.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1316.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1317.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1318.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1319.png)

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

Example 1 Request a dispatch to TT 720

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1320.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1321.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1322.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1323.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1324.png)

Which results in returning to FORM_JOB_LIST or FORM_IDLE, the form the CHE was on previous to FORM_ DISPATCH.

[***\*2.4.2.17\****](2.4.2.17) ***\*CHE Search Tr\*******\*uck Jobs\****

***\*Objective:\****

A CHE driver wants to search for all jobs for a particular truck.

***\*PreCondition:\****

The current formID is "FORM_JOB_LIST" or FORM_IDLE

***\*PostCondition:\****

The returned formID is "FORM_SELECT_TRUCK_JOB" which lists the jobs for a particular truck based on inputted truck license or internal truck ID.

***\*Example Search\**** ***\*Message:\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1325.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1326.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1327.png)

Response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1328.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1329.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1330.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1331.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1332.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1333.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1334.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1335.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1336.png)

 AREA_TYPE="YardRow" type="to" DOOR="A" transport="720" transportInArea="Y"/>    

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1337.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1338.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1339.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1340.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1341.png)

The next expected message would be a job selection of one of these truck jobs for the CHE to perform.

[***\*2.4.2.18\****](2.4.2.18) ***\*CHE Swap\**** ***\*Posi\*******\*tion\****

***\*Objective:\****

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

Signal that the containers are in a swapped orientation on the spreader prior to set to keep inventory accurate.

***\*PreCondition:\****

CHE is on the FORM_LADEN_AT_DEST, FORM_DISPATCH, or FORM_DISPATCH_LIFTED.

***\*PostCondition:\****

CHE is pushed the same formId with the container JPOS attributes swapped.

***\*Example Swap\****

Previous form:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1342.png)

<che CHID="702" equipType="STRAD" MTEU="2" OPMD="SELF_COMPLETE" twinCapable="Y"     

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1343.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1344.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1345.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1346.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1347.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1348.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1349.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1350.png)

 <container EQID="TEST0000001" LNTH="20" QWGT="30000" MNRS="" QCAT="E" EQTP="2000"   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1351.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1352.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1353.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1354.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1355.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1356.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1357.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1358.png)

 <container EQID="TEST0000002" LNTH="20" QWGT="30000" MNRS="" QCAT="E" EQTP="2000"   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1359.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1360.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1361.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1362.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1363.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1364.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1365.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1366.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1367.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1368.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1369.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

Indicate a swapped position

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1370.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1371.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1372.png)

Which results in the following from ECN4

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1373.png)

<che CHID="702" equipType="STRAD" MTEU="2" OPMD="SELF_COMPLETE" twinCapable="Y"     

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1374.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1375.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1376.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1377.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1378.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1379.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1380.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1381.png)

 <container EQID="TEST0000002" LNTH="20" QWGT="30000" MNRS="" QCAT="E" EQTP="2000"   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1382.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1383.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1384.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1385.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1386.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1387.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1388.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1389.png)

 <container EQID="TEST0000001" LNTH="20" QWGT="30000" MNRS="" QCAT="E" EQTP="2000"   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1390.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1391.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1392.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1393.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1394.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1395.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1396.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1397.png)

<displayMsg msgID="585">Swapped TEST0000002 is FWD and TEST0000001 is AFT</displayMsg> 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1398.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1399.png)

[***\*2.4.2.19\****](2.4.2.19) ***\*CHE ChangeListM\*******\*ode 2727\****

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

***\*Objective:\****

Toggle container lists (used on FORM_CONFIRM_CONTAINER and FORM_TBDUNIT_CONFIRM_CONTAINER)  between single and twin display modes. The mode represents the spreader mode, as either "TWIN" or "UNKNOWN." Toggling these will toggle between a container list given as single container jobs or twin jobs.

***\*PreCondition:\****

The CHE is on FORM_CONFIRM_CONTAINER or FORM_TBDUNIT_CONFIRM_CONTAINER

***\*PostCondition:\****

The CHE remains on the same FORM, but the display mode has been updated.

***\*Example\**** ***\*1 CHE ChangeListMode\**** ***\*UNKNOWN to TWIN\****

Current form and option list is in "UNKNOWN" mode:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1400.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1401.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1402.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1403.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1404.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1405.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1406.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1407.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1408.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1409.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1410.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1411.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1412.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1413.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1414.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1415.png)

The user toggles the option list mode:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1416.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1417.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1418.png)

Response from ECN4: current form with the option-list type as "TWIN"

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1419.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1420.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1421.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1422.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1423.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1424.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1425.png)

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

***\*Example 2 CHE Change\*******\*ListMode from TWIN to\**** ***\*UNKNOWN\****

Current form and option list is in "TWIN" mode:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1426.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1427.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1428.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1429.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1430.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1431.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1432.png)

The user toggles the option list mode:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1433.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1434.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1435.png)

Response from ECN4: current form with the option-list type as "UNKNOWN":

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1436.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1437.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1438.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1439.png)

 <unit index="1"><container EQID="TEST0000001" LNTH="20" QWGT="30000" MNRS=""     

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1440.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1441.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1442.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1443.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1444.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1445.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1446.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1447.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1448.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1449.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1450.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1451.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1452.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1453.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1454.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1455.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1456.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1457.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1458.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1459.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1460.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1461.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1462.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1463.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1464.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1465.png)

***\*Notes\****

n If there are no twin jobs, the returned twin mode option-list may will not contain any units.

 

[***\*2.4.2.20\****](2.4.2.20) ***\*CHE Self-Ass\*******\*ign 2640\****

***\*Objective:\****

CHE operator wants to self-assign a yard block using ECN4

***\*PreCondition:\****

The current formID is "FORM_IDLE"

***\*PostCondition:\****

The CHE is assigned to a yard block based on the request from the CHE operator to assign a yard block. ***\*Example: Self-Assign CHE\**** ***\*Range\****

Request message sent from ECN4 to ECN4 Web:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1466.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1467.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1468.png)

Which results in the following response message from ECN4 to ECN4 Web:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1469.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1470.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1471.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1472.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1473.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1474.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1475.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1476.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1477.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1478.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1479.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1480.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1481.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1482.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1483.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

 

 

***\*2.4.3\**** ***\*Example\**** ***\*TT\**** ***\*Messages\****

l [CHE Pull Trailer 2630](#bookmark304)  [***\*81\****](#bookmark305)

l [CHE Park Trailer 2630](#bookmark306)  [***\*85\****](#bookmark307)

l [CHE Park And Wait](#bookmark308)  [***\*88\****](#bookmark309)

l [CHE TT Progress Update2630 and 2633](#bookmark310)  [***\*92\****](#bookmark311)

l [CHE TT Signals Container Lifted](#bookmark312)  [***\*97\****](#bookmark313)

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

***\*2.4.3.1 CHE\**** ***\*Pull Trailer 2630\****

l [CHE Pull Laden Trailer](#bookmark314)  [***\*81\****](#bookmark315)

l [CHE Pull Unladen Trailer](#bookmark316)  [***\*83\****](#bookmark317)

 

 

***\*CHE\**** ***\*Pull\**** ***\*Laden Trailer\****

***\*Objective:\****

Report to ECN4 that the TT has attached to a Laden trailer.

***\*PreCondition:\****

The current formID is "FORM_TRUCK_EMPTY_AT_ORIGIN". The from position area type indicates that the move is from a wheeled location and therefor the TT is required to attach to a laden trailer.

***\*PostCondition:\****

n CHE Status = LoggedOn and empty, not attached to a trailer.

n CHE JobContainerID = TESTU000001

In version 2.6, support for pull by chassis id was added: *CHE JobChassisID = TESTU000001

***\*Example TT\**** ***\*Pull\**** ***\*Laden Tr\*******\*ailer 2630\****

Previous response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1484.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1485.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1486.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1487.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1488.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1489.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1490.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1491.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1492.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1493.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1494.png)

 <position PPOS="C2381.A" AREA="C238" AREA_TYPE="YardRow" type="to" DOOR="?"/>    

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1495.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1496.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1497.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1498.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1499.png)

In the above code sample, the attribute pullable='Y' indicates that the CHE should Pull the job.

 

| ***\*pullable\**** | ***\*Description\****                                        |
| ------------------ | ------------------------------------------------------------ |
| Y                  | The position in the from PPOS, WB211 in the above example, is one which the TT can attach from |

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

| ***\*pullable\**** | ***\*Description\****                                        |
| ------------------ | ------------------------------------------------------------ |
| not present        | The from PPOS is not a pullable position, for example a ground position. |

TT Pull Laden Trailer.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1500.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1501.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1502.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1503.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1504.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1505.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1506.png)

***\*Note:\**** In the above code sample, formId is not provided, external systems do not need to provide this. It is for ecn4web  to keep in sync with ecn4, we do not expect external systems to keep the states in sync, but do expect they will wait for a response before sending the next message.

CHE Pull Laden Trailer - Response from ECN4 - Che carrying job.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1507.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1508.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1509.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1510.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1511.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1512.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1513.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1514.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1515.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1516.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1517.png)

 <position PPOS="C2381.A" AREA="C238" AREA_TYPE="YardRow" type="to" DOOR="?"/>    

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1518.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1519.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1520.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1521.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1522.png)

Before release 2.6, laden trailers could only be pulled and parked by container ID. Now in N4 2.6 pull and parks by chassisID are accepted:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1523.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1524.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1525.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1526.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1527.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

***\*Note:\**** In the above code sample, action="Pull" can be shortcut by action="U".

***\*CHE\**** ***\*Pull\**** ***\*Unladen Trailer\****

***\*Objective:\****

Report to ECN4 that the TT is attached to an unladen trailer.

***\*PreCondition:\****

The current formID is "FORM_TRUCK_Pull_TRAILER". Previously the TT was FORM_TRUCK_IDLE, and then was dispatched a job from a ground slot but the TT is not attached to a trailer. This requires the TT be attached to a trailer. The dispatch took the TT to FORM_TRUCK_Pull_TRAILER.

***\*PostCondition:\****

n CHE Status = LoggedOn and Idle

n CHE JobContainerId = none

For descriptions of the elements and attributes refer to the xmlRdtResponse.xsd and the xmlRdtRequest.xsd.

***\*Example\**** ***\*1 TT\**** ***\*Pull\**** ***\*Non Specif\*******\*ic\**** ***\*Unladen Trailer 2630\****

Previous response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1528.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1529.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1530.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1531.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1532.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1533.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1534.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1535.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1536.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1537.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1538.png)

 <position PPOS="331A01.A" AREA="331A" AREA_TYPE="YardRow" type="to" DOOR="A"/> 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1539.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1540.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1541.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1542.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1543.png)

TT Pull Non Specific Unladen Trailer.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1544.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1545.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1546.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1547.png)

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1548.png)

CHE Pull Unladen Trailer - Response from ECN4 - Proceed with the job.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1549.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1550.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1551.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1552.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1553.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1554.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1555.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1556.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1557.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1558.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1559.png)

 <position PPOS="331A01.A" AREA="331A" AREA_TYPE="YardRow" type="to" DOOR="A"/> 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1560.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1561.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1562.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1563.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1564.png)

***\*Example 2 TT\**** ***\*Pull Specific\**** ***\*Unladen Trailer 2630\****

Previous response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1565.png)

TT Pull Specific Unladen Trailer.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1566.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1567.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1568.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1569.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1570.png)

CHE Pull Unladen Trailer - Response from ECN4 - Proceed with the job.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1571.png)

***\*Notes\****

n If the CHASSIS field is present but empty the user will receive an error message that the trailer is invalid. 3rd

party systems that intent to send a pull for a bombcart (untracked chassis) should not include the chassis attrib- ute in the message. Pulls from the ASC transfer zones for a decoupled operation need to specify the chassis ID.

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

***\*2.4.3.2 CHE\**** ***\*Park Trailer 2630\****

l [CHE Park Laden Trailer](#bookmark318)  [***\*85\****](#bookmark319)

l [CHE Park Unladen Trailer](#bookmark320)  [***\*86\****](#bookmark321)

 

 

***\*CHE\**** ***\*Park\**** ***\*Laden Trailer\****

***\*Objective:\****

Report to ECN4 that a TT has Parked a Laden trailer.

***\*PreCondition:\****

The current formID is "FORM_TRUCK_LADEN_AT_DEST". The to position area type indicates that the TT is required to park the trailer with the container.

***\*PostCondition:\****

n CHE Status = LoggedOn and carrying a container

n CHE JobContainerId = TESU0000001

In version 2.6, park by chassis id was supported, in this example: CHE JobChassisId = TESU0000001

For descriptions of the elements and attributes refer to the xmlRdtResponse.xsd and the xmlRdtRequest.xsd.

***\*Example TT\**** ***\*Park\**** ***\*Laden Tr\*******\*ailer 2630\****

Previous response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1572.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1573.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1574.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1575.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1576.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1577.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1578.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1579.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1580.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1581.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1582.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1583.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1584.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1585.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1586.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1587.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1588.png)

In the example above the attribute, parkable="Y", indicates how the TT may complete the job:

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

| ***\*Parkable\**** | ***\*Description\****                                        |
| ------------------ | ------------------------------------------------------------ |
| Y                  | The TT may complete the job by parking, and that the to PPOS - in the above case WC241 is a valid pos- ition in which it can be parked. |
| N                  | The TT may complete the job by parking, but the to PPOS is not a valid position in which the container can be parked, for example in the case of a rail load, the to PPOS would be a rail car slot. |
| Not Preset         | The TT may not complete the job by parking. The job must be completed by another CHE such as an RMG fork or ASC. |

At landside transfer zones the value of GENCHS controls parkable = Y:

 

| ***\*GENCHS\**** | ***\*Description\****                                        |
| ---------------- | ------------------------------------------------------------ |
| Y                | TTs may disconnect at the transfer zone rather than wait for move completion. That is a decoupled oper- ation. |
| N                | TTs must wait at the transfer zone for move completion. That is a live load operation. |

TT Park Laden Trailer. Note PDS vendors are not expected to send the TOS pull or park (or lift or set) messages with the formID, that is for a front end UI (ecn4web or similar) to keep in sync with ecn4.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1589.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1590.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1591.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1592.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1593.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1594.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1595.png)

CHE Park Unladen Trailer - Response from ECN4 - Che Idle.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1596.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1597.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1598.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1599.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1600.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1601.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1602.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1603.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1604.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1605.png)

In version 2.6, support was added for parking laden trailers by chassis id only:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1606.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1607.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1608.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1609.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1610.png)

***\*CHE\**** ***\*Park\**** ***\*Unladen Trailer\****

***\*Objective:\****

Report to ECN4 that a TT has parked an unladen trailer.

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

***\*PreCondition:\****

The current formID is "FORM_TRUCK_PARK_TRAILER". Previously the TT was FORM_TRUCK_IDLE, and then was dispatched a job from a wheeled slot requiring the TT to park its current trailer. The dispatch took the TT to FORM_

TRUCK_PARK_TRAILER.

***\*PostCondition:\****

n CHE Status = LoggedOn and Idle

n CHE JobContainerId = none

For descriptions of the elements and attributes refer to the xmlRdtResponse.xsd and the xmlRdtRequest.xsd.

***\*Example TT\**** ***\*Park\**** ***\*Unladen Trail\*******\*er 2630\****

Previous response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1611.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1612.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1613.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1614.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1615.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1616.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1617.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1618.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1619.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1620.png)

 <position PPOS="WB211" AREA="WB" AREA_TYPE="YardRowWheeled" type="from" DOOR="?"/> 

 <position PPOS="WC241" AREA="WC" AREA_TYPE="YardRowWheeled" type="to" DOOR="?"/>  

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1621.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1622.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1623.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1624.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1625.png)

(note in the above the TT is unladen, the job it has been dispatched has move stage "PLANNED") TT Park Unladen Trailer.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1626.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1627.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1628.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1629.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1630.png)

CHE Park Unladen Trailer - Response from ECN4 - Proceed with the job.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1631.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1632.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1633.png)

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1634.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1635.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1636.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1637.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1638.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1639.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1640.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1641.png)

 <position PPOS="WB211" AREA="WB" AREA_TYPE="YardRowWheeled" type="from" DOOR="?"/> 

 <position PPOS="WC241" AREA="WC" AREA_TYPE="YardRowWheeled" type="to" DOOR="?"/>  

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1642.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1643.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1644.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1645.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1646.png)

***\*Notes\****

n See CHE Fetch Unladen Trailer 2630

 

***\*2.4.3.3 CHE\**** ***\*Park And\**** ***\*Wait\****

***\*Objective:\****

Report to ECN4 that the TT is attached to laden chassis and parked and waiting at the rail transfer zone for rail load.

This is a message used for a specific case where a TT has pulled a laden chassis to go to the rail transfer zone for a

RLOD job. When the TT reaches the rail transfer zone, the driver signals to ECN4 that the TT will park and wait for the  move to be completed, rather than parking and leaving. This is also used for RDSCH flows, where a TT is attached to a bare chassis and arrives in the rail transfer zone to wait for a discharge.

***\*PreCondition:\****

TT is logged in and on form FORM_TRUCK_LADEN_AT_DEST. Decoupled operations are being run (EC Parameter GENCHS is set to "Y"). There is an optimal TZ slow in the rail transfer zone.

***\*PostCondition:\****

Che status logged on and carrying a container, parked but chassis is still connected to TT

***\*Example\**** ***\*1 TT\**** ***\*ParkAndWait\**** ***\*laden\**** ***\*chassis for rail\**** ***\*load\****

The previous message:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1647.png)

<che CHID="TRK1" equipType="TRUCK" MTEU="2" DSTA="L1MD" OPMD="TRUCK" APOW="RAIL"    

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1648.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1649.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1650.png)

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1651.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1652.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1653.png)

<work count="1" moveStage="CARRY_UNDERWAY" planningIntent="SINGLE" chassisQual="C">   

 <job MVKD="RLOD" pow="" age="7" priority="N" shift="0" moveStage="CARRY_UNDERWAY">  

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1654.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1655.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1656.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1657.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1658.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1659.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1660.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1661.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1662.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1663.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1664.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1665.png)

ParkAndWait request:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1666.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1667.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1668.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1669.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1670.png)

ECN4 response:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1671.png)

<che CHID="TRK1" equipType="TRUCK" MTEU="2" DSTA="L1MD" OPMD="TRUCK" APOW="RAIL"    

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1672.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1673.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1674.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1675.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1676.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1677.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1678.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1679.png)

 <job MVKD="RLOD" pow="RAIL" age="17" priority="N" shift="0" moveStage="PLANNED">    <container EQID="TEST0000001" LNTH="40" QWGT="30000" MNRS="" QCAT="M" EQTP="2200"   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1680.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1681.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1682.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1683.png)

 refID="Y.LBCT:RC1.1.004." AREA="RC" AREA_TYPE="YardRailTZ" type="from" DOOR="U"/>  

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1684.png)

 refID="R.TRAIN_01:CAR_02.1.1.B" AREA="RAIL" AREA_TYPE="Rail" type="to" DOOR="Y"/>  

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1685.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1686.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1687.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1688.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1689.png)

***\*Example 2 TT\**** ***\*ParkAndWait\**** ***\*bare chassis for rail discharge\****

The previous message:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1690.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1691.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1692.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1693.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1694.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1695.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1696.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1697.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1698.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1699.png)

<work count="1" moveStage="CARRY_UNDERWAY" planningIntent="SINGLE" chassisQual="S">   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1700.png)

 <container EQID="TAZU23433" LNTH="45" QWGT="4581" MNRS="" QCAT="M" isTrailer="Y"     EQTP="BC" HGHT="2896" LOPR="MSC" TRKC="" ACRR="TRUCK" DCRR="VESSEL" RLSE="" RFRT=""    CCON="" ISHZ="N" DWTS="1" ISGP="CH" GRAD="" RMRK="" JPOS="CTR" PUTJPOS="CTR"/>    

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1701.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1702.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1703.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1704.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1705.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1706.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1707.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1708.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1709.png)

ParkAndWait request:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1710.png)

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2.4** **Example** **XMLRDT** **Mes****sages**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1711.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1712.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1713.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1714.png)

ECN4 response:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1715.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1716.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1717.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1718.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1719.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1720.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1721.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1722.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1723.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1724.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1725.png)

 <job MVKD="RDSC" pow="RAIL" age="4" priority="N" shift="0" moveStage="PLANNED">     <container EQID="TANU8796235" LNTH="20" QWGT="25000" MNRS="" QCAT="E" EQTP="20GP"   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1726.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1727.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1728.png)

 refID="R.3CAE3:3CBE7002.A.1.B" AREA="RAIL" AREA_TYPE="Rail" type="from" DOOR="U"/> 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1729.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1730.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1731.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1732.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1733.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1734.png)

***\*Notes\****

n ParkedAndWaiting attribute is non persistent attribute for che, and is set once the TT is parked and waiting and reset on container lift/drop. There is a ***\*parkable\**** attribute included on the position node:

 

| ***\*Parkable\**** | ***\*Description\****                                        |
| ------------------ | ------------------------------------------------------------ |
| Y                  | The TT may complete the job by parking, and that the to PPOS - in the above case WC241 is a valid pos- ition in which it can be parked. |
| Not Preset         | The TT may not complete the job by parking. The job must be completed by another CHE such as an RMG fork or ASC. |
| N                  | The TT may complete the job by parking, but the to PPOS is not a valid position in which the container can be parked, for example in the case of a rail load, the to PPOS would be a rail car slot. |

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

For interchange with end-loading ASCs, the value of EC Parameter GENCHS controls parkable = Y:

 

| ***\*GENCHS\**** | ***\*Description\****                                        |
| ---------------- | ------------------------------------------------------------ |
| Y                | TTs may disconnect at the transfer zone rather than wait for move completion. That is a decoupled oper- ation. |
| N                | TTs must wait at the transfer zone for move completion. That is a live load operation. |

 

 

***\******** CHE TT\**** ***\*Progress\**** ***\*Update 2630 a\*******\*nd 2633\****

***\*Objective:\****

Report to ECN4 that the TT has completed a particular part of the job. Causes ECN4 to advance the CHE to the next part of the job. For TTs, progress updates only support transitions FORM_TRUCK_EMPTY_TO_ORGIN to FORM_

TRUCK_EMPTY_AT_ORIGIN, FORM_TRUCK_EMPTY_TO_ORIGIN to FORM_CONFIRM_TRUCK_LANE, FORM_ CONFIRM_TRUCK_LANE to FORM_TRUCK_EMPTY_AT_ORIGIN and FORM_TRUCK_LADEN_TO_DEST to

FORM_TRUCK_LADEN_AT_DEST. From an operational point of view, it is not necessary to supply TT progress

updates, the impact of not doing so is that dispatch scheduling decisions will be made with non-optimal data. Driving statistics will also be impacted.

***\*PreCondition:\****

The TT is in a "TO" state.

***\*PostCondition:\****

The TT is in an "AT" state or advances to FORM_CONFIRM_TRUCK_LANE.

***\*Example1 CHE\**** ***\*Progress\**** ***\*Update\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1735.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1736.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1737.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1738.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1739.png)

CHE Progress Update - Response from ECN4 - formID has been advanced, in this case the previous formID was FORM_TRUCK_EMPTY_TO_ORIGIN.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1740.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1741.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1742.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1743.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1744.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1745.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1746.png)

 <position PPOS="303A02.A" AREA="303A" AREA_TYPE="YardRow" type="from" DOOR="F"/>  

 <position PPOS="331A02.A" AREA="331A" AREA_TYPE="YardRow" type="to" DOOR="F"/>   

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1747.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1748.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1749.png)

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2.4** **Example** **XMLRDT** **Mes****sages**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1750.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1751.png)

***\*Example 2: CHE\**** ***\*Progress\**** ***\*Update to a yard\**** ***\*block with a transtainer type transfer zone\**** ***\*block\****, for example a CARMG or ASC block:

Note: For moves to automated crane blocks FORM_TRUCK_EMPTY_AT_ORIGIN and FORM_TRUCK_LADEN_AT_ DEST contain a transferZone node.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1752.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1753.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1754.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1755.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1756.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1757.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1758.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1759.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1760.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1761.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1762.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1763.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1764.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1765.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1766.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1767.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1768.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1769.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1770.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1771.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1772.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1773.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1774.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1775.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1776.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1777.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1778.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1779.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1780.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1781.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1782.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2.4** **Example** **XMLRDT** **Mes****sages**

 

 

CHE Progress Update - Response from ECN4 - formID has been advanced to FORM_CONFIRM_TRUCK_LANE

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1783.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1784.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1785.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1786.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1787.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1788.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1789.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1790.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1791.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1792.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1793.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1794.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1795.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1796.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1797.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1798.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1799.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1800.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1801.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1802.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1803.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1804.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1805.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1806.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1807.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1808.png)

***\*Example 3: CHE\**** ***\*Progress\**** ***\*Update with\**** ***\*Lane Confirmation while T\*******\*T\**** ***\*is\**** ***\*LADEN_TO\****.

A lane confirmation is used when the TT will be arriving in a wheeled transfer zone, such as pickup or drop off from an ASC stack via the landside transfer zone.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1809.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1810.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1811.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1812.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1813.png)

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

CHE Progress Update with Lane Confirmation - Response from ECN4 - formID has been advanced. In this example TT 'ITV08' is in landside transfer zone in lane "A" as a result ASC will get dispatched to fetch container from the truck. In

this case ECN4 will add a "lane" attribute in position element of type "to" only.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1814.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1815.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1816.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1817.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1818.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1819.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1820.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1821.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1822.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1823.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1824.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1825.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1826.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1827.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1828.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1829.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1830.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1831.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1832.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1833.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1834.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1835.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1836.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1837.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1838.png)

***\*Example 4: CHE\**** ***\*Progress\**** ***\*Update with\**** ***\*Lane Confirmation while\**** ***\*TT\**** ***\*is\**** ***\*EMPTY_TO\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1839.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1840.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1841.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1842.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1843.png)

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

CHE Progress Update with Lane Confirmation - Response from ECN4 - formID has been advanced. In this example TT 'ITV08' is in landside transfer zone in lane "A" as a result ASC will get dispatched to put container on the truck. In this

case ECN4 will add a "lane" attribute in position element of type "from" only.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1844.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1845.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1846.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1847.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1848.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1849.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1850.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1851.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1852.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1853.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1854.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1855.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1856.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1857.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1858.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1859.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1860.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1861.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1862.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1863.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1864.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1865.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1866.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1867.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1868.png)

***\*Example 5: CHE\**** ***\*Progress\**** ***\*Update\**** ***\*with TZ,\**** ***\*used for a side-loading Automated Yard Crane\**** ***\*flow\****

TT position update to TZ

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1869.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1870.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1871.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1872.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1873.png)

The next expected message would be a progress update with the lane in block AL that the TT in which the TT is waiting.

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

TT position update to TP

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1874.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1875.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1876.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1877.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1878.png)

***\******** CHE TT Signals\**** ***\*Container\**** ***\*Lift\*******\*ed\****

***\*Objective:\****

Report to ECN4 that the TT has reached the destination, and that the container(s) which were carried have been lifted.  The message is used to free the TT when the other equipment operators have not completed the job in the absence of  yard cranes reporting lifts to ECN4. This message is also used at the QC, to allow TTs to free before the vessel load has confirmation by the hatch clerk has been completed.

***\*PreCondition:\****

The current formID is "FORM_TRUCK_LADEN_AT_DEST".

***\*PostCondition:\****

CHE Status = IDLE

***\*Example TT Signals Container\**** ***\*Lifted 2630\****

Previous response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1879.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1880.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1881.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1882.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1883.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1884.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1885.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1886.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1887.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1888.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1889.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1890.png)

 <position PPOS="1845" AREA="1845" AREA_TYPE="ITV" type="from" DOOR="?"/>    

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1891.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1892.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1893.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1894.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1895.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1896.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

TT Signals Container Lifted 2630

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1897.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1898.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1899.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1900.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1901.png)

CHE Signals Container Lifted - Response from ECN4.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1902.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1903.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1904.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1905.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1906.png)

***\*Notes\****

n After this operation, the move stage of the job is "CARRY_COMPLETE", the job however remains on the

RMG/RTG joblist and will not be completed until either the RMG completes it, or in the case of a vessel load, the job is completed to the vessel slot by the hatch clerk.

n When the TT is carrying 2 containers, the signals container lifted message applies to both containers if they are to the QC or to the same yard section. When the TT is laden with containers to two different yard sections, the  signal lifted only applies to the container planned to the current yard section where the TT is.

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2.4** **Example** **XMLRDT** **Mes****sages**

 

 

***\*2.4.3.6 CHE TT\**** ***\*lane\**** ***\*information for vessel\**** ***\*load an\*******\*d discharge\****

***\*Objective:\****

Signal from ECN4 that TT has dispatched to or arrived at the QC lane for a vessel load/discharge.The lane number,   obtained from the ***\*'qcTruckLane'\**** attribute in the position element, is displayed in ECN4Web along with the QC name.

***\*PreCondition:\****

\1.  The current formID is FORM_TRUCK_LADEN_TO_DEST or FORM_TRUCK_LADEN_AT_DEST.

\2.  Applicable only for vessel load and discharge operations.

\3.  The AREA_TYPE must be ***\*Vessel\****.

***\*Example\**** ***\*1: TT\**** ***\*is dispatched to QC for vessel\**** ***\*l\*******\*oading\****

response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1907.png)

***\*Example 2: TT arrived at QC\**** ***\*for vessel\**** ***\*loading\****

response from ECN4:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1908.png)<message formId="FORM_TRUCK_LADEN_AT_DEST" ack="Y" MSID="12">

<che CHID="CYT7" equipType="TRUCK" MTEU="2" DSTA="IDAV" OPMD="TRUCK" APOW="G03"

CHASSIS="BOMBCART" status="Working" locale="en_US" location="G03" refID-

D="Y.MAIN:???..." userID="test" ManuallyDispatched="Y">

<pool name="YARD"><list count="2" type="pow"><pow name="G03" mode="Manual" />

<pow name="UNASSIGNED" mode="Manual" />

</list>

</pool>

<work count="1" moveStage="CARRY_UNDERWAY" planningIntent="SINGLE">

<job MVKD="LOAD" pow="G03" age="27881" priority="N" moveStage="CARRY_UNDERWAY">

<container EQID="JOSU2100401" LNTH="20" QWGT="15000" MNRS="" QCAT="E" EQTP="2200" HGHT-

T="2591" LOPR="JOS" TRKC="" ACRR="TRUCK" DCRR="JOS_VV1" RLSE="" RFRT="" CCON="" ISHZ-

Z="N" DWTS="338" ISGP="GP" GRAD="" RMRK="" JPOS="CTR" PUTJPOS="CTR" />

<position PPOS="CYT7" refID="E.MAIN:CYT7..." AREA="CYT7" AREA_TYPE="ITV" type="from"

DOOR="U" TKPS="1" />

***\*<position PPOS="G03-L1" qcTruckLane="L1"\**** refID="V.JOS_VV1:A.69.03.78" AREA="G03" AREA_

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1909.png)

In these response messages, ***\*<position\**** ***\*PPOS="G03-L1" qcTruckLane="L1">\****is the position element where G03 is the QC name and L1 is the lane number.

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**3** **Geodetic Positions**

**2****.4** **Example** **XMLRDT** **Mes****sage****s**

 

 

***\*3 Geodetic\**** ***\*Positions\****               

ECN4 has supported third party systems sending spreader events (lifts and sets) using geodetic coordinates since N4 2.2 and it also supports sending positions using Geodetic and Cartesian data format in the outbound XMLRDT

response as of N4 3.1. Note that the Cartesian and Geodetic Data format is only supported for Rail and Rail transfer

Zone positions. Spatial coordinates will often result in mapping to more than one bin in the N4 yard model. EC post pro- cesses these values to map to a slot specific location, similar to how the refID is interpreted and then mapped to a valid PPOS N4 bin location.

In This Section

l [Example Geodetic Messages](#bookmark322)  [***\*102\****](#bookmark323)

l [CHE Coordinate Conversion (not supported)](#bookmark324)  [***\*108\****](#bookmark325)

l [Lift     Truck     Spatial Bin Filteringfromand](#bookmark326)   [***\*113\****](#bookmark327)

l [Tier Calculation](#bookmark328)  [***\*114\****](#bookmark329)

l [Geodetic Configuration](#bookmark330)  [***\*116\****](#bookmark331)

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**3** **Geodetic Positions**

**3.****1** **Example Geodetic Message****s**

 

 

***\*3.1\**** ***\*Example\**** ***\*Geodetic\**** ***\*Messages\****

***\*Example Geodetic\**** ***\*Lift\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1910.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1911.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1912.png)

 <geodeticData Latitude='128.3342' Longitude='48.1234' Height='260' Heading='245.5'/> 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1913.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1914.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1915.png)

***\*Example Geodetic Set\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1916.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1917.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1918.png)

 <geodeticData Latitude='128.3342' Longitude='48.1234' Height='260' Heading='245.5'/> 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1919.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1920.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1921.png)

***\*Example Cartesian\**** ***\*Lift with\**** ***\*different yard origin\**** ***\*ID (OriginID='ABB')\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1922.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1923.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1924.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1925.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1926.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1927.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1928.png)

***\*Example Cartesian\**** ***\*Lift\**** ***\*r\*******\*esponse with different yard origin\**** ***\*ID (OriginID='ABB')\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1929.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1930.png)

 userID="1234" range="WB07 1>>1 10>>1, WB07 1>>1 10>>;1">  

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1931.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1932.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1933.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1934.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1935.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1936.png)

 <job MVKD="RLOD" pow="RAIL" age="11" priority="N" shift="0" moveStage="PLANNED"> 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1937.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1938.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1939.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1940.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1941.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1942.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1943.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1944.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1945.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1946.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1947.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1948.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1949.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1950.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1951.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1952.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1953.png)

***\*Example Cartesian\**** ***\*Drop with\**** ***\*different yard origin\**** ***\*ID (OriginID='ABB')\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1954.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1955.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1956.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1957.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1958.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1959.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1960.png)

***\*Example of Outbound\**** ***\*Job\**** ***\*list XMLRDT message with Cartesian and\**** ***\*Geodetic\**** ***\*Data for\**** ***\*a\**** ***\*position\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1961.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1962.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1963.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1964.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1965.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1966.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1967.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1968.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1969.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1970.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1971.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1972.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1973.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1974.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1975.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1976.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1977.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1978.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1979.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1980.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1981.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1982.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1983.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1984.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1985.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1986.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1987.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1988.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1989.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1990.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1991.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1992.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1993.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1994.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1995.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1996.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1997.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1998.png)

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps1999.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2000.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2001.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2002.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2003.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2004.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2005.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2006.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2007.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2008.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2009.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2010.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2011.png)

***\*Example of Joblist response  with different yar\*******\*d origin\**** ***\*ID (OriginID='ABB')\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2012.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2013.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2014.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2015.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2016.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2017.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2018.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2019.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2020.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2021.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2022.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2023.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2024.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2025.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2026.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2027.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2028.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2029.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2030.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2031.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2032.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2033.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2034.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2035.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2036.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2037.png)

***\*Example of request selecting a job for dispatch with dif\*******\*ferent yard origin\**** ***\*ID\**** ***\*(OriginID='ABB')\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2038.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2039.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2040.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2041.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2042.png)

***\*Example of response selecting a job for dispatch with dif\*******\*ferent yard origin\**** ***\*ID\**** ***\*(OriginID='ABB')\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2043.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2044.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2045.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2046.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2047.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2048.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2049.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2050.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2051.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2052.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2053.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2054.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2055.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2056.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2057.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2058.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2059.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2060.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2061.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2062.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2063.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2064.png)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2065.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2066.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2067.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2068.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2069.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2070.png)

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**3** **Geodetic Positions**

**3.2** **CHE Coordinate Conversion (****not** **supported)**

 

***\*3.2\**** ***\*CHE\**** ***\*Coordinate\**** ***\*Conversion\**** ***\*(\*******\*not\**** ***\*sup\*******\*-\**** ***\*ported\*******\*)\****

***\*ECN4 does\**** ***\*not support CHE coordinate conversion\**** and therefore some form of middleware is required to perform this conversion for ECN4. The below describes the conversion work that is not implemented.

The geodetic and telemetry information received will contain the following components.

n Latitude and Longitude ofthe CHE GPS Antenna.

n Trolly reference value, typically a measurement of the trolly in distance from a start point on the CHE.

n Hoist reference value, typically a measurement of the amount of "cable" spooled out from the CHE.

n Compass Heading ofthe CHE.

By themselves these values do not represent the center point of a spreader, which is ultimately what is required to

return a specific bin (yard location) from Spatial. Consequently these values need to be converted and adjusted.

In order to determine how best to calculate the adjusted value we'll break this down into a few discrete components.

The first part will be to view the location from above the CHE in the horizontal plane. Think of this as a bird looking down on the CHE.

The location of the GPS antenna, lets call that ***\*A\****L, is the starting point. Note that this is not the location of the CHE RDT screen or the CHE cab where the operator is located! What we need to then reference is the CHE center point

or ***\*CHE\****cp, which should always be cons**i**dered the x = 0 and **y** = 0 starting point. Each ***\*A\****L value is also unique to each CHE and their relationship to the ***\*CHE\****cp should always be in the context to the "front" of CHE.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2071.png) 

But wait, is it correct to assume that the CHE center point is the same as the Spreader Center Point? The answer is no, it is not correct to assume that. That is because in some CHE, especially those like Yard Cranes, the Spreader Center Point changes dynamically in relationship to the CHE center point. And technically, this is the case for all known CHE  today i.e. straddle carrier and front end loader spreaders can move horizontally, but the amount they move is not con-  sidered enough to matter for this type of location.

Consequently there as another value, which is supplied called the **trolly** measurement.

The **trolly** represents how far the trolly has extended from a fixed point on the CHE. And this is only represented in a lin- ear format, but could be in both a positive and negative direction. So, this means we have one more fixed to reference  called the ***\*Trolly\**** fp which only has a **x** coordinate offset from the ***\*CHE\****cp. Then to find the Spreader center point ***\*S\****cp we  then use the supplied **trolly** as referenced to the ***\*Trolly\**** fp.

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2072.png)**3** **Geodetic Positions** **CHE Coordinate Conversion (not** **sup****ported)**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2073.png) 

And when we combine things we can view the model as such.

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2074.jpg) 

 

That seems really complicated right? Well, what are we saying here? We need to find only one value, which is a vari- able, and that is the distance from the ***\*A\****L ***\*to the S\****cp.

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2075.jpg) 

 

Then using the heading ofthe CHE we can use some very simple code to convert the Latitude and Longitude from the ***\*A\****L ***\*to\**** ***\*the\**** ***\**\**** ***\*S\****cp.

***\*Note:\**** A mistake was made here. The heading is not sufficient. The bearing of the ***\*A\****L ***\*to the \* S\****cp.

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2076.jpg) 

 

Formula:

lat2 = asin(sin(lat1)*cos(d/R) + cos(lat1)*sin(d/R)*cos)

lon2 = lon1 + atan2(sin*sin(d/R)*cos(lat1), cos(d/R)?sin(lat1)*sin(lat2))

d/R is the angular distance (in radians), where d is the distance travelled and R is the earth's radius JavaScript to convert destination point given distance and bearing from start point

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2077.png)var lat2 = Math.asin ( Math.sin (lat1)*Math.cos (d/R) +

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2078.png)

var lon2 = lon1 + Math.atan2 (Math.sin (brng)*Math.sin (d/R)*Math.cos (lat1),        

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2079.png)

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

To view nice examples of what the above formula can do, check out this website: ***\**Moveable\**\*** ***\**Type Scripts\**\***

([***\**http://www.movable-type.co.uk/scripts/latlong.ht\**\******\**ml)\**\***](http://www.movable-type.co.uk/scripts/latlong.html)

But wait, there is more. At this point, we only have the Latitude and Longitude that Spatial needs to return a slot specific bin. Spatial also requires the height above the ground. This is where the last bit of telematics data comes into play,

the **hoist** value from the CHE.

The **hoist** is similar in concept to the **trolly**. It is a measure of the distance that has been paid out of a cable reel from a   set point on the CHE. We'll call this the hoist fixed point ( ***\*H\**** fp), which needs to be a referenced height above the ground. Note as well, that in the below example, the hoist was drawn this way on purpose. We cannot assume that the hoist

value is in a vertical line to the ( ***\*H\**** fp), which means there should be a calibration factor that allows for the resulting value of the spreader height above the ground (***\*S\****hbg).

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2080.jpg) 

***\*Twin Carry Orientations\****

 And One More Thing.

What about twin carry? How does the model work with that? The answer is that the model needs to be further improved. Starting with the height above the Earth model, we can see how it differs.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2081.jpg) 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

And we can see that the good news with hoist is that we are not so concerned with the twin spreader heights, since they will always be the same as the ***\*S\****hbg.

The problem comes into play when we look at the ***\*S\****cp and the distance calculations from the ***\*A\****L. In the below diagram we can see why.

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2082.jpg) 

 

***\*Note:\**** As with the ***\*S\****cp the bearing of the twin center points must be calculated from the ***\*A\****L

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**3** **Geodetic Positions**

**3.3** **Lift** **from** **Truck** **and Sp****atial** **Bin Filterin****g**

 

 

***\*3.3\**** ***\*Lift\**** ***\*from\**** ***\*Truck\**** ***\*and\**** ***\*Spatial\**** ***\*Bin\**** ***\*Filtering\****

Special consideration needs to be applied now to differentiate between a lift from a truck versus a yard location. In the simplest model the lift event explicitly identifies the CHE, however that will not be a common case. Instead, the system will need to derive a lift of a CHE based upon the spreaderHeight attribute. This is especially important when the loc-  ation is within a block itself, like when a FEL lifts from an external truck inside a stack.As seen in the below model, the  stack contents are evaluated to help determine the tier attribute. In the case of a truck, the stack height is always zero, the spread height would be range of values, like 3535mm to 3840mm.

Spatial bins may result in heaps, a specific unique slot value, or non specific slots (more than one slot returned). In the  last case, this will result in a position of "NO_SLOT." When a NO_SLOT lift event is identified as not coming a truck, the containers found to be in the vicinity to the location are used for the CHE operator to confirm which was lifted. These will appear on the FORM_CONFIRM_CONTAINER.

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**3** **Geodetic Positions**

**3.4  Tier Calculation**

 

 

***\*3.4\**** ***\*Tier\**** ***\*Calculation\****

An interesting problem arises when trying to calculate the tier of a stack when only the spreader height is given. This is because a bin does not contain tier information. What can also be seen in the diagram below is that the height of con-  tainers does not lend itself to a straight mapping of heights to tiers. In fact, if the stack was high enough, eventually the 9th tier of high cubes would be equal to the 10th tier of standard height containers.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2083.png) 

Consequently the idea is that ECN4 will need to consider the contents of the BIN in order to determine the proper stack tier. This is fairly straight forward then. Based upon the stack contents the current stack height can be determined,

which could be a value of zero should it be empty. Then if the event is a lift, the height should be equal to the stack

height. Unless it was empty, in which case it would mean one of two things, bad inventory or a lift from a truck. For a set event, the height of the container that was on the spreader needs to be added to the current stack height.

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2084.png) 

Some discrepancies can arise where the values are not equal. It can be expected that some deviation within say 5cm is acceptable. But it is also possible for larger deviations that might be as much as 100cm. These are likely to be the result of the following situations. One the event is related to a truck, not a yard bin. Two, the inventory is incorrect. Or three,

the calibration of the CHE height is in need of adjustment.

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**3** **Geodetic Positions**

**3.5** **Geodetic Configuration**

 

 

***\*3.5\**** ***\*Geodetic\**** ***\*Configuration\****

\1.  ECN4 settings file (ecn4_settings.xml) should be updated to reflect the OPERATION type desired for geodetic operations.

a.  Open "ecn4_settings.xml" file.

b.  Find a component named "geodetic" under ECN4 category.

c.  Make sure the setting called "OPERATION" is set to "remote" as below.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2085.png) 

\2.  The default inbound and outbound Geodetic message filters should be specified in ECN4 state model (state- model.xml) file as highlighted below:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2086.png) 

Note that the inbound Geodetic message filter supports Lift, Drop, PositionUpdate and Pull actions.

The statemodel comes with default inbound and outbound message filters. So you don't need to make changes unless you want to overwrite the default implementation or the default setting was previously removed and want to restore it.

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**4** **refID** **Positions**

**3.5** **Geodetic Configuration**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2087.png)***\*4\**** ***\*refID\**** ***\*Positions\****

ECN4 supports sending refID based positions allowing for 3rd-party vendors to process positional information in a

standardized form ignorant of the yard file naming scheme. SPARCS ECN4 previously accepted only the position

information in PPOS and geodetic formats. There was a problem with 'PPOS', as the rules for formatting positional

information depends upon the SPARCS ECN4 yard file configuration, which varies from site-to-site. Thus, it was difficult for the vendors to implement their location solutions at different N4 sites as they need to know how each site requires

positional information to be formatted, based upon the SPARCS yard file setup.

The refID message intercepts inbound messages and parses refID to PPOS. If positional information is sent with Geo- detic and refID, ECN4 gives preference to refID information to resolve position. If a non-null PPOS value is passed in

the message it takes precedence over both refId and geodetic co-ordinates. This acts as an override mechanism. To

resolve the position when the geo positions are specified, ECN4 pre-configures Geodetic Component Provider with con- nectivity to spatial database in N4.

If positional information is sent with Geodetic and refId, The Ecn4 will give preference to refId information to resolve pos- ition. If ECN4 cannot resolve position from refID then it considers Geodetic position information to resolve position.

When Geodetic Position is send in XMLRDT message ECN4 assumes that the SPATIAL DATABASE in N4 is fully con- figured to resolve position based on Lat-long.

refID-based position information adheres to the following general syntax:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2088.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2089.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2090.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2091.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2092.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2093.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2094.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2095.png)

When using the nVMT application, a CHE can also have a refID attribute in the CHE Job List payload, where the refID   represent the last known position of the CHE as a concatenation of block/column/row/tier. This enables nVMT to display the CHE's last known position as a section view of the block. If the position is empty or blank, then the refID will not be  output.

Here's an example of a refID attribute for a CHE:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2096.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2097.png)

 

In This Section

l [refID Syntax](#bookmark333)  [***\*118\****](#bookmark334)

l [Special use cases          refIDconcerning](#bookmark335)  [***\*120\****](#bookmark336)

l [Example     MessagesrefId](#bookmark337)  [***\*122\****](#bookmark338)

l [refID Configuration](#bookmark339)  [***\*127\****](#bookmark340)

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**4** **refID** **Positions**

**4.1** **refID** **Synta****x**

 

 

***\*4.1\**** ***\*refID\**** ***\*Syntax\****

***\*Yard variants (Y)\****

***\*Slotted\**** ***\*Block\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2098.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2099.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2100.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2101.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2102.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2103.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2104.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2105.png)

***\*Heap\**** ***\*Block\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2106.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2107.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2108.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2109.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2110.png)

***\*Vessel variant (V)\****

V.<vessel name>:<vessel deck>.<vessel bay>.<vessel bay row>.<vessel bay column>     

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2111.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2112.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2113.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2114.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2115.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2116.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2117.png)

***\*Rail variant\**** ***\*(R)\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2118.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2119.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2120.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2121.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2122.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2123.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2124.png)

***\*Truck variants (T,\**** ***\*E)\****

***\*Over-the-road Truck (T)\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2125.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2126.png)

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**4.1** **refID** **Synta****x**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2127.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2128.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2129.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2130.png)

***\*Internal Truck\**** ***\*(E)\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2131.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2132.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2133.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2134.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2135.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2136.png)

TRUCK related position attributes CHID, TKPS and TRKL

If the position information for truck is sent with CHID or TRKL and TKPS then ECN4 will ignore the information parsed from the refID and use these values passed as override.

n TRKL - Truck License Number for OTR

n CHID - Che Id for UTR

n TKPS - Container Position on Truck

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**4** **refID** **Positions**

**4.2** **Special use cases concerning refID**

 

 

***\*4.2\**** ***\*Special\**** ***\*use\**** ***\*cases\**** ***\*concerning\**** ***\*refID\****

***\*Fail to\**** ***\*Deck\****

When there is a Fail to Deck, the FLD is included in the ECN4 response:

<position PPOS="FAIL" refID="Y.OAK:FLD..." AREA_TYPE="YardBlock" type="to" DOOR="Y" TKPS="1" />

***\*NO_SLOT\**** ***\*position\****

The NO_SLOT is a position that does not have any yard location in model. ECN4 processes this information

differently. The lifts from the terminal tractor or sets to terminal tractors are translated internally to PPOS=NO_SLOT.  ECN4 also looks for a CHID=Terminal Tractor Id or TRKL=Truck License Number attribute to determine if any Terminal Tractor is associated with this move.

***\*When\**** ***\*is\**** ***\*NO_SLOT value applied?\****

ECN4 will interpret inbound XMLRDT request refID information as a "NO_SLOT" value under any of the following con- ditions:

refID is invalid (not in the format "<qualifier>.<location>:<block>.<row>.<column>.<tier>"), or does not match the yard configuration being run against).

Example: <position PPOS='' refID='XXXXXX' CHID='724' JPOS='FWD'/> refID attribute is null

Example: <position PPOS='' refID='' CHID='724' JPOS='FWD'/> refID is a non-Yard qualifier type.

Example: refID not of type "Y.<yard code>:<block>.<row>.<column>.<tier>"

***\*Note\****: only ***\*(Y)ard\**** types can map refID values to non-NO_SLOT PPOS value ***\*refID and Confirm\**** ***\*Container\****

A lift from a terminal tractor transitions to CONFIRM_CONTAINER if there is no yard model position associated with the Terminal Tractor, since it is not possible for ECN4 to determine the container lifted. In this case, the refID that holds the terminal tractor information will translate to PPOS=NO_SLOT and the CHID is set to the terminal tractor ID or TRKL-

L=Truck License Number.

***\*Note:\**** The multi-item CONFIRM_CONTAINER functionality is not applicable in case of refID based PDS.

***\*NO_SLOT\**** ***\*lift event\****

Prior to the job list support, a lift event from NO_SLOT results to the required operator to verify the container that was lif- ted. With this, ECN4 identifies where a CHE lifts from trucks. In these scenarios the lift event is not from a logical loc-

ation, and automatically results in a NO_SLOT value. Instead of forcing container information, ECN4 automatically determines what was lifted. This occurs in two main forms:

\1. If the job is previously selected then it is assumed that the lift container was dispatched job.

\2. If the truck ID is provided in the lift event, ECN4 can use that to verify which work instruction is to be transitioned. ***\*PDS\**** ***\*Exception\**** ***\*Handling\****

The PDS lift message is directed to a specific slot and not a container because many PDS providers do not have an inventory. ECN4 only knows the location where the event has occurred.

When a lift from a slot is reported, ECN4 expects the reason for the container which is located within that slot. The exception cases happen with three types of failures.

When there is more than one container within the slot.

No container is found in the slot.

The slot was not recognized (NO_SLOT)

In all of these conditions, ECN4 handles the exception case in the same way. The below framework allows the different terminal rules to work:

When ECN4 is unable to resolve a lift to a specific container, it requests the operator a confirmation for the container which is just lifted.

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**4.2** **Special use cases concerning refID**

 

 

ECN4 displays a list and defaults to the first container. This container will be associated with the work instruction dis- patched to the CHE.

If a terminal's labor rules stipulate that a CHE operator cannot record inventory or enter container numbers, this solution should mitigate a jurisdiction issue. In this case the operator does not need to enter a container number. Instead the

operator can select a new container from the list provided.

ECN4Web is built to allow CHE operators to access the Confirm Container Lifted functionality without PDS. This allows non-PDS CHE operators an ability to override jobs which are previously dispatched by PrimeRoute or otherwise.

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**4** **refID** **Positions**

**4.****3** **Example refId** **Mess****age****s**

 

 

***\*4.3\**** ***\*Example\**** ***\*refId\**** ***\*Messages\****

l [Example     LiftrefId](#bookmark342)  [***\*122\****](#bookmark343)

l [Example refID Set](#bookmark344)  [***\*122\****](#bookmark345)

l [Example refID Position Update](#bookmark346)  [***\*123\****](#bookmark347)

l [Example Terminal Tractor Messages      refIDusing](#bookmark348)   [***\*123\****](#bookmark349)

l [Example Truck (OTR) Messages       refIDusing](#bookmark350)  [***\*124\****](#bookmark351)

l [Fail to Deck responses using refId](#bookmark352)  [***\*126\****](#bookmark353)

 

 

***\*4.3.1\**** ***\*Example\**** ***\*refId\**** ***\*Lift\****

Lift Message

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2137.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2138.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2139.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2140.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2141.png)

Lift Message (NO_SLOT)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2142.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2143.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2144.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2145.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2146.png)

Twin Lift Message

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2147.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2148.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2149.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2150.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2151.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2152.png)

***\*4.3.2\**** ***\*Example\**** ***\*refID\**** ***\*Set\****

Set Message

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2153.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2154.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2155.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2156.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2157.png)

Set Message (NO_SLOT)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2158.png)

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**4.****3** **Example refId** **Mess****age****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2159.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2160.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2161.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2162.png)

Twin Set Message

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2163.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2164.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2165.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2166.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2167.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2168.png)

***\*4.3.3\**** ***\*Example\**** ***\*refID\**** ***\*Position\**** ***\*Update\****

Position Update Message

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2169.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2170.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2171.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2172.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2173.png)

Twin Position Update Message

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2174.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2175.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2176.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2177.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2178.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2179.png)

***\*4.3.4\**** ***\*Example\**** ***\*Terminal\**** ***\*Tractor\**** ***\*Messages\**** ***\*using\**** ***\*refID\****

***\*Terminal Tractor\**** ***\*Lift\****

Terminal Tractor Lift Message (Extracted CHID, TKPS)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2180.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2181.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2182.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2183.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2184.png)

***\*Notes\****

n refID will be processed as a NO_SLOT position

n CHID will be extracted from the refID ("1831" in this example)

n TKPS will be extracted from the refID ("1" in this example)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**4.****3** **Example refId** **Mess****age****s**

 

 

Terminal Tractor Lift Message (Defined CHID, TKPS)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2185.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2186.png)

 <position PPOS="" refID="E.MAIN:1831...1" CHID="1832" TKPS="2" JPOS="CTR"/>     

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2187.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2188.png)

***\*Notes\****

n refID will be processed as a NO_SLOT position

n CHID attribute will be processed ("1832" in this example)

n TKPS attribute will be processed ("2" in this example) ***\*Terminal Tractor Set\****

Terminal Tractor Set Message (Extracted CHID, TKPS)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2189.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2190.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2191.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2192.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2193.png)

***\*Notes\****

n refID will be processed as a NO_SLOT position

n CHID will be extracted from the refID ("1831" in this example)

n TKPS will be extracted from the refID ("1" in this example) Terminal Tractor Set Message (Defined CHID, TKPS)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2194.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2195.png)

 <position PPOS="" refID="E.MAIN:1831...1" CHID="1832" TKPS="2" JPOS="CTR"/>     

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2196.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2197.png)

***\*Notes\****

n refID will be processed as a NO_SLOT position

n CHID attribute will be processed ("1832" in this example)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2198.png)n TKPS attribute will be processed ("2" in this example) ***\*Terminal Tractor Progress\**** ***\*Update\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2199.png)![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2200.jpg)   ***\*ProgressUpdate\**** message for Terminal Tractor is not supported

***\*4.3.5\**** ***\*Example\**** ***\*Truck\**** ***\*(\*******\*OTR\*******\*)\**** ***\*Messages\**** ***\*using\**** ***\*refID\****

***\*Truck\**** ***\*Lift\****

Truck Lift Message (Extracted TRKL, TKPS)

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**4.****3** **Example refId** **Mess****age****s**

 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2201.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2202.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2203.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2204.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2205.png)

***\*Notes\****

n refID will be processed as a NO_SLOT position

n TRKL will be extracted from the refID ("LIC1831" in this example)

n TKPS will be extracted from the refID ("1" in this example) Truck Lift Message (Defined TRKL, TKPS)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2206.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2207.png)

 <position PPOS="" refID="T.MAIN:LIC1831...1" TRKL="LIC1832" TKPS="2" JPOS="CTR"/>  

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2208.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2209.png)

***\*Notes\****

n refID will be processed as a NO_SLOT position

n TRKL attribute will be processed ("LIC1832" in this example)

n TKPS attribute will be processed ("2" in this example) ***\*Truck Set\****

Truck Set Message (Extracted TRKL, TKPS)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2210.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2211.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2212.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2213.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2214.png)

***\*Notes\****

n refID will be processed as a NO_SLOT position

n TRKL will be extracted from the refID ("LIC1831" in this example)

n TKPS will be extracted from the refID ("1" in this example) Truck Lift Message (Defined TRKL, TKPS)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2215.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2216.png)

 <position PPOS="" refID="T.MAIN:LIC1831...1" TRKL="LIC1832" TKPS="2" JPOS="CTR"/>  

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2217.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2218.png)

***\*Notes\****

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**4.****3** **Example refId** **Mess****age****s**

 

 

n refID will be processed as a NO_SLOT position

n TRKL attribute will be processed ("LIC1832" in this example)

n TKPS attribute will be processed ("2" in this example)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2219.png)***\*Truck\**** ***\*Progress\**** ***\*Update\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2220.png)![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2221.jpg)   ***\*ProgressUpdate\**** message for Truck is not supported.

***\*4.3.6\**** ***\*Fail\**** ***\*to\**** ***\*Deck\**** ***\*responses\**** ***\*using\**** ***\*refId\****

If your site uses refId, and you have named logical blocks for FLD and EC, Failed-to-Deck responses appear as follows:

***\*For\**** ***\*FLD:\****

<position PPOS="FLD" refID="Y.OAK:FDEC..." AREA_TYPE="YardBlock" type="to" DOOR="Y" />

***\*For\**** ***\*EC:\****

<position PPOS="EC" AREA_TYPE="YardBlock" refID="Y.OAK:EQPC..." type="to" DOOR="Y" />

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**4** **refID** **Positions**

**4.4** **refID Configuration**

 

 

***\*4.4\**** ***\*refID\**** ***\*Configuration\****

The default inbound refID message filter should be specified in ECN4 state model (statemodel.xml) file as highlighted below:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2222.png) 

Note that refID positions are supported for Lift, Drop and PositionUpdate actions.

The statemodel comes with the default refID filter called "RefIDMessageInboundFilter". So you don't need to make changes unless you want to overwrite the refID filter or was previously removed and want to restore it.

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**5** **Narrowband Serial** **PDS**

**4.4** **refID Configuration**

 

 

***\*5\**** ***\*Narrowband Serial\**** ***\*PDS\****           

In This Section

l [Narrowband Support for ECN4 (ANSI Terminal)](#bookmark354)  [***\*129\****](#bookmark355)

l [Narrowband Components    Configurationand](#bookmark356)  [***\*130\****](#bookmark357)

l [Narrowband User Interface](#bookmark358)  [***\*131\****](#bookmark359)

l [Serial PDS](#bookmark360)  [***\*133\****](#bookmark361)

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**5** **Narrowband Serial** **PDS**

**5.1** **Narrowband Support** **forECN4 (ANSI Ter****minal)**

 

 

***\*5.1\**** ***\*Narrowband\**** ***\*Support\**** ***\*for\**** ***\*ECN\*******\*4 (\*******\*ANSI\**** ***\*Ter\*******\*-\**** ***\*minal\*******\*)\****

A standard ECN4-Web install supports web browsers in a WiFi (broadband) environment and Psion TekLogix TESS ter- minals in a narrowband environment. The latter does not support push and has reached End Of Life.

To provide a better solution for narrow band, Navis provides an adapter template configuration for ANSI telnet sup-

porting devices. An ANSI terminal is a device-independent narrowband protocol that gets support from LXE, Teklogix,  and many other devices. With this feature, ECN4Web-defined forms convert to a format that displays abbreviated com- mands, and text for ANSI terminals. Additionally, ECN4Web supports Push functionality in ANSI terminals without

manual refresh. It behaves just as the Web UI does with respect to the asynchronous messages. This feature expands the list of supported devices to any that can run ANSI Terminal emulators. The minimum size screens supported are

40x8 and 20x16. The conversion of web screens to terminal screens is done automatically and transparently with all expected behaviors, such as input validation, working as expected, only in a text UI.

From the user perspective, the same functionality provided to web browsers is available via a text (ANSI) terminal. Nar- rowband supports push updates, multiple character based screens, and customization via web technologies in the

same way broadband support does. Function keys are supported, in contrast to broadband where function keys are

used by the OS for other shortcuts and not strictly by the web browser. ECN4-Web supports UTF-8, but the proper dis- play of non-ASCII character sets is not guaranteed on narrowband screens.

With ANSI Terminal, two options will be provided:

n Secure Shell (SSH) — recommended

n Telnet — support for legacy devices that cannot run a SSH client

The telnet protocol offers no encryption. Our implementation cannot address this deficiency in the telnet protocol—it does not add or detract from current security at facilities that rely on telnet in Radio Server. We therefore recommend using the SSH option.

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**5** **Narrowband Serial** **PDS**

**5.2** **Narrowband Components and Con****figuration**

 

 

***\*5.2\**** ***\*Narrowband\**** ***\*Components\**** ***\*and\**** ***\*Con\*******\*-\****

***\*figuration\****

See the EC Install and Dispatch Guide for configuration instructions for setting up the adapater. This section describes the components for further customizations, beyond the Navis provided adapter template.

ECN4-Web narrowband employs five key components:

n ***\*XSLT\**** ***\*Device Adapter template\****: serving ANSI Terminal devices will be primarily done via existing device adapter infrastructure.

n ***\*SSH daemon:\**** provided by the open source MINA SSHD library.

n ***\*Telnet daemon\****: a telnet connection is stateful; it will be managed by the ***\*telnetd\**** open source library.

n ***\*Text GUI\**** constructed by the ***\*Lante\*******\*rna\**** open source library.

n ***\*Groovy\**** ***\*UI configuration:\**** the device adapter template output will be a runtime compiled ***\*Groovy\**** class that constructs a text UI using the aforementioned Lanterna library.

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2223.png) 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**5** **Narrowband Serial** **PDS**

**5.3** **Narrowband User** **Interface**

 

 

***\*5.3\**** ***\*Narrowband\**** ***\*User\**** ***\*Interface\****

Terminal screens will map directly to existing web screens. For smaller screens, some truncation, renaming and prun- ing of labels will be necessary.

Existing prototype screen capture, showing the login screen:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2224.png) 

Existing web version of the job list screen, and its text screen equivalent, using truncation, abbreviation and function keys:

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2225.jpg) 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**5** **Narrowband Serial** **PDS**

**5.4** **Serial** **PD****S**

 

 

***\*5.4\**** ***\*Serial\**** ***\*PDS\****

Many terminal operators have CHE that use Serial PDS where data is sent to the VMT over the serial port (RS232).

ECN4's support of Serial PDS relies on a telnet narrowband agent (as described above) to be deployed on the VMT

such as TeraTerm, TekTerm, Putty, or Windows MS Dos. ECN4-Web submits the current form with the Serial message once a valid Serial message format is identified. ECN4 filters this Serial message, similar to filtering for geodetic or refID filtering, and converts the message to a valid XMLRDT PDS message. As Serial PDS relies on a telnet client, it uses the narrowband adapter as described in the diagram below:

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2226.png) 

The diagram above indicates how data flows from the PDS box all the way to ECN4 and the response back to the VMT. The green line indicate the data flow. The description of the work flow and transformation is listed below:

A. Serial message send from PDS box to VMT via RS232 connector (serial cable).

B. The Serial message is read from the VMT serial port and forwarded to ECN4-Web telnet client residing in the VMT. Keyboard wedge like tool is used to read the data from the serial port and write it to the current ECN4-Web screen.

C. The Serial message is saved as a hidden field (parameter) and the current screen has a mechanism to submit itself if the String is a valid serial message.

D. ECN4-Web (server side) will receive this parameter and creates an outbound XMLRDT message which includes the parameter and then sends it to ECN4 through the XMLRDT channel.

E. ECN4 reads the XMLRDT message and checks the action type of the XMLRDT message. If the action indicates PDS serial message it creates a PDS XMLRDT message from the string and continues normal execution like any other

XMLRDT message. Once the message is processed the resulting XMLRDT message is sent to ECN4-Web.

F. ECN4-Web transforms the XMLRDT message and the result of the transformation is directed to the CHE via telnet connection.

***\*5.4.1\**** ***\*Serial\**** ***\*Message\**** ***\*Format\****

The message format for the character pattern must conform to the below:

n All fields use ASCII characters.

n Numbers are represented either via the numeral, or via "26 code".

n Field types are "c" for character data and "(26)" for 26-code numeric data

n Positions are relative to the start of the message, not the start of the field.

 

***\*5.4.1.1\**** ***\*Message format for PDS to\**** ***\*RD\*******\*T messages\****

 

| ***\*Position\**** | ***\*#\*******\*bytes\**** | ***\*type\**** | ***\*contents\****                 |
| ------------------ | -------------------------- | -------------- | ---------------------------------- |
| 01                 | 1                          | c              | "{" for LXE, ASCII 10 for Teklogix |

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

| ***\*Position\**** | ***\*#\*******\*bytes\**** | ***\*type\**** | ***\*contents\****                                           |
| ------------------ | -------------------------- | -------------- | ------------------------------------------------------------ |
| 02                 | 1                          | c              | message identifier (rotate "0" - "9"): Rotate "0" - "9". SPARCS used to return the same iden- tifier in its acknowledgement.ECN4 does nothing with this field. |
| 03                 | 1                          | c              | message type ("1" - "9", “M”, “A”)                           |
| 04                 | 1                          | c              | Status char: PDS vendor may attach any desired meaning to this. ECN4 simply logs it. |
| 05                 | 24                         | c              | data field                                                   |
| 29                 | 1                          | c              | "}"                                                          |
| 30                 | 1                          | c              | chr(13) carriage return                                      |

 

***\*5.4.1.2 Container position elements (message types\**** ***\*“1” –\**** ***\*“5”)\****

The data field has the following structure. Positions are relative to the start of the message, not the start of the field.

 

| ***\*Position\****                                           | ***\*#\*******\*bytes\**** | ***\*type\**** | ***\*contents\****                                           |
| ------------------------------------------------------------ | -------------------------- | -------------- | ------------------------------------------------------------ |
| 05                                                           | 7                          | c              | yard stack name, left justified and padded on the right with spaces, char(32) |
| 12                                                           | 1                          | c              | The tier name, left justified and padded. This is usually either "1", "2", etc.; or "A", "B", etc.This information is used by ECN4 for automated inventory when PDSTRT is set to Y, otherwise it is ignored. |
| 13                                                           | 1                          | c              | spreader mode ("2", "4", "5", “D”, “T”, “L”, or “R”)         |
| 14                                                           | 1                          | c              | position reliability ("0" - "9"). The PDS vendor may define it, for instance, "1" is excellent, and "9" is very bad.ECN4 does nothing with this field, however, customizations may be written to use this information. |
| ***\*For message types\**** ***\*1, 2,\**** ***\*and\**** ***\*3\**** |                            |                |                                                              |
| 15                                                           | 3                          | c              | a - deviation. Not used by ECN4.                             |
| 18                                                           | 3                          | c              | b - deviation. Not used by ECN4.                             |
| 21                                                           | 4                          | (26)           | x coordinate in decimeters from an arbitrary origin near the center of the yard using base 26. Not used by ECN4. |
| 25                                                           | 4                          | (26)           | y coordinate in decimeters. Not used by ECN4.                |
| ***\*For message types 4 and 5 (only\**** ***\*implemented for weight\**** ***\*detection,\**** ***\*container\**** ***\*I\*******\*D\**** ***\*is\*******\*present for OCR spreader capability).\**** |                            |                |                                                              |
| 15                                                           | 12                         | c              | Container ID can be filled in by PDS systems that can identify the container lifted. Not used by ECN4. |
| 27                                                           | 2                          | (26)           | Weight in quintals represent 0 - 67.5 tonnes and weight in excess of 67.5 tonnes should be set to maximum of 67.5.If load cells are present, the weight may be passed along with the lift or set down pos-  ition. This is a "26 code" value representing quintals (0.1 tonne). For message types "4" and "5", values from 0 to 67.5 tonnes may be represented. If a weight exceeds 67.5tonnes then the value is set to "ZZ". If a weight has not been taken set the value to "aa" for missing data. |

 

 

***\*5.4.1.3\**** ***\*Message format for RDT to\**** ***\*PD\*******\*S messages\****

 

| ***\*pos\**** | ***\*#\*******\*bytes\**** | ***\*type\**** | ***\*contents\****                      |
| ------------- | -------------------------- | -------------- | --------------------------------------- |
| 01            | 1                          | c              | "{" for LXE;  ASCII 10 for Teklogix     |
| 02            | 1                          | c              | identifier for this message ("0" - "9") |

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

| ***\*pos\**** | ***\*#\*******\*bytes\**** | ***\*type\**** | ***\*contents\****                         |
| ------------- | -------------------------- | -------------- | ------------------------------------------ |
| 03            | 1                          | c              | message type ("8"- message, OK "9"- error) |
| 04            | 1                          | c              | identifier of acknowledged message         |
| 05            | 1                          | c              | "}"                                        |
| 06            | 1                          | c              | chr(13) carriage return                    |
| 07            | 1                          | c              | chr(10) line feed                          |

 

 

***\********\**** ***\*Message Type\****

 

| ***\*Message\*******\*Type\**** | ***\*Description\****                                        |
| ------------------------------- | ------------------------------------------------------------ |
| 1                               | Lift                                                         |
| 2                               | Set                                                          |
| 3                               | Position Update                                              |
| 4                               | Lift with container weight                                   |
| 5                               | Set with container weight                                    |
| 6                               | Update unit weight. This is the equivalent of the updateUnitWeight XMLRDT message and allows for sys- tems to set the unit weight of the dispatched container.When measuring the weight of containers that are twin lifted, it is necessary to specify the weights of the  individual FWD or AFT containers. Measuring the weight of the FWD container uses the "L" spreader pos- ition, while the spreader position "R" will update the weight of the AFT container. |
| D                               | In SPARCS this field was used to allow PDS systems to simulate a "Next Step" when being dispatched by Prime Route.ECN4 does nothing with this field, however, customizations may be written to use this information. |
| 8, 9                            | Used for RDT to PDS messages.                                |
| M, A                            | Used for customer specific capabilities.ECN4 does nothing with this field, however, customizations may be written to use this information. |

 

 

***\*5.4.1.5 Yard Stack\**** ***\*Name\****

This is the user's name for the stack. This must exactly match the form of stack positions displayed in the TOS. These complexities must be considered:

n Stack names must follow the conventions for 20 and 40 foot stack names in the TOS.

n If stack name is fewer than 7 characters, the balance should be padded on the right with spaces.

n N4 is able to generate a complete set of legal position names using the XPS menu item File>Diagnostics>Yard Positions. Containers set to positions other than these will appear in the generic "yard" heap.

n If the position is outside of a defined stack, then "NO SLOT" should be passed.

 

***\******** Spreader\**** ***\*Mode\****

The spreader mode field encodes the length of a container, whether or not the lift is a twin lift and when available, the rel- ative positions of the two containers on the spreader.

The two ends of the spreader are defined "L" and "R." If the spreader on the lifting equipment has a natural orientation  with respect to the operator then the ends should be labeled consistently for all equipment of that type. For example, on a top-pick the spreader is on the front of the equipment. So, the left and right ends of the spreader would be labeled "L" and "R" respectively.

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

In twin lift mode the spreader is oriented according to the lowest string value of the position. For example, if a pair of con- tainers are lifted from 34BHH11A and 34BHH12A with the container at 34BHH11A on the "L" end of the spreader. Then the length code for the lift is "L" If both containers are put simultaneously in positions 34BHH05A and 34BHH06A with

the container on the "R" end being placed in position 34BHH05A then the length code for the put is "R"

 

| ***\*Spreader\**** ***\*Mode\**** | ***\*Description\****                                        |
| --------------------------------- | ------------------------------------------------------------ |
| 2                                 | One 20 footer.                                               |
| 4                                 | One 40 footer.                                               |
| 5                                 | One 45 footer.                                               |
| T                                 | Two 20 footers: the orientation of the spreader to the yard positions is not known. |
| L                                 | Corresponds to the "FWD" JPOS position in XMLRDT.            |
| R                                 | Corresponds to the "AFT" JPOS position in XMLRDT.            |

 

 

***\******** Serial\**** ***\*Input\**** ***\*Delimitter\****

Please notice the difference in specification for LXE vs. Teklogix. For LXE, we use braces:

 

| ***\*pos\**** | ***\*#\*******\*bytes\**** | ***\*type\**** | ***\*contents\**** |
| ------------- | -------------------------- | -------------- | ------------------ |
| 01            | 1                          | c              | "{"                |
| 02            | 1                          | c              | "}"                |

For Teklogix, ASCII 10 replaces the first brace:

 

| ***\*pos\**** | ***\*#\*******\*bytes\**** | ***\*type\**** | ***\*contents\**** |
| ------------- | -------------------------- | -------------- | ------------------ |
| 01            | 1                          | c              | 10                 |
| 02            | 1                          | c              | "}"                |

 

 

***\*5.4.1.8 26-Code\****

These are numbers written using base 26. It compactly represents integers using the alphabetic ASCII characters only. "A" represents the digit "0" and "Z" represents the digit "25" etc. Lowercase characters represent negative numbers.

There should be no numbers represented using mixed case digits, but the left-most digit determines the sign of the num- ber regardless. A 26-code field containing all lower case "a" indicates missing data. Some examples are:

 

| ***\*26-\*******\*code\**** | ***\*Value\****      |
| --------------------------- | -------------------- |
| AA                          | 0                    |
| AB                          | 1                    |
| AZ                          | 25                   |
| BA                          | 26                   |
| ZZ                          | 675 = (25 * 26 + 25) |
| BAA                         | 676 = (26 * 26)      |
| aaa                         | "No Data"            |
| aab                         | -1                   |
| aaz                         | -25                  |
| aba                         | -26                  |

Two, three, and four character 26-codes are used in the interface. Fields are right justified and padded on the left with the appropriately signed "A" e.g. "A" for positive numbers and "a" for negative numbers.

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**6** **Updating Yard** **Measured Weight**

**5.4** **Serial** **PDS**

 

 

***\*6\**** ***\*Updating Yard\**** ***\*Measured Weight\****    

In This Section

l [Container Scales    Weightand](#bookmark363)  [***\*138\****](#bookmark364)

l [Updating Yard Measured Weight](#bookmark365)  [***\*139\****](#bookmark366)

l [Example Yard Measured Weight Messages](#bookmark367)  [***\*140\****](#bookmark368)

l [Yard Measured Weight Configuration](#bookmark369)  [***\*142\****](#bookmark370)

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**6** **Updating Yard** **Measured Weight**

**6.1** **Container** **Scales and Weight**

 

 

***\*6.1\**** ***\*Container\**** ***\*Scales\**** ***\*and\**** ***\*Weight\****

A container's weight is one of the most important attributes for planning aboard a vessel and allows terminals to segreg- ate properly. Since the reported weight can be inaccurate, establishing the actual container weight helps in preventing  two types of situations that can be costly. The first situation involves a CHE that lifts a container that is above the rated  Safe Weight Limit. If a CHE attempts to lift a load that is above the SWL this can cause equipment damage and put at   risk personnel should there be a catastrophic failure. The second situation involves stowage aboard vessels. If the con- tainer weight is off, then the stability plan for a ship will be incorrect. In the most extreme situation this could actually

cause the vessel to be unseaworthy. If a terminal had to correct this it would cause significant loss in time by having to re-stow containers. Even worse, the vessel could set sail and be lost at sea.

Many terminals utilize truck scales to capture the actual weight. While these truck scales are effective at the in-gate they do not capture containers that arrive by rail or by vessel. For sites where the volume of transshipments are high they typ- ically utilize a scale device that is aboard the CHE itself. These scale devices are becoming common place as a means  for preventing the first situation described above. By passing that scale information to the TOS, the second situation can also be prevented as well.

Another benefit is that these CHE scales are more accurate than the truck scales as truck scales weight the com-

bination of the tractor, chassis, and container. The tractor weights are estimated and depending on the site, the chassis weights might be estimated as well. For the CHE scales, only the container is weighed. Some PDS supported container handling equipment have additional sensors to capture the lifted container weight. In principle, what is happening on the CHE is that the PDS supplier is simply reporting the weight being lifted by the spreader.

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**6** **Updating Yard** **Measured Weight**

**6.2** **Updati****ng Yard** **Measured Weigh****t**

 

 

***\*6.2\**** ***\*Updating\**** ***\*Yard\**** ***\*Measured\**** ***\*Weight\****

This lifted weight is stored against the container as the Yard Measured Weight. It does not update the Container's Gross Weight. The Weight Discrepancy attribute is the calculated difference between the Yard Measured Weight and the Con- tainer's Gross weight. Configurable business rules within N4 make the determination as to what should happen based  upon the Weight Discrepancy, Yard Measured Weight, and the Gross Weight.

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**6** **Updating Yard** **Measured Weight**

**6.3** **Example Yard** **Measured Weight** **M****essages**

 

 

***\*6.3\**** ***\*Example\**** ***\*Yard\**** ***\*Measured\**** ***\*Weight\**** ***\*Mes\*******\*-\**** ***\*sages\****

ECN4 supports setting the yard measured weight either during lift, during set, or other time while CHE is dispatched. Update Yard Measured Weight During Lift

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2227.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2228.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2229.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2230.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2231.png)

Update Yard Measured Weight During Set

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2232.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2233.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2234.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2235.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2236.png)

 

Update Yard Measured Weight

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2237.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2238.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2239.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2240.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2241.png)

 

Update Yard Measured Weight -Twin case updating AFT container

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2242.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2243.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2244.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2245.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2246.png)

Update Yard Measured Weight - Twin case updating FWD container

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2247.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2248.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2249.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2250.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2251.png)

We can also update the yard measured weight using serial PDS messages. This is useful for customers using Serial PDS systems.

Example of yard measured weight using "updateUnitWeight" action. The main parts are highlighted in colors as follows

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



 

 

***\*Legend:\****

n Red color indicates the action and this is mapped to action "Y" in the XMLRDT message

n Green color indicates the JPOS

n Blue color indicates the weight in base 26

***\*Serial String for a s\*******\*ingle\**** ***\*container:\****

Given the serial string: {061331A07 A21555888ABCDWXAZ} The resulting XMLRDT message is:

Update Yard Measured Weight - Twin case updating FWD container

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2252.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2253.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2254.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2255.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2256.png)

***\*Serial String for a twin mode\**** ***\*in forward\**** ***\*position:\****

Given the serial string: {061321A16 2L1555888ABCDWXZZ} The resulting XMLRDT message is:

Update Yard Measured Weight - Twin case updating FWD container

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2257.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2258.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2259.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2260.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2261.png)

***\*Serial String for a twin mode\**** ***\*in forward\**** ***\*position:\****

Given the serial string: {061321A16 2R1555888ABCDWXAR} The resulting XMLRDT message is:

Update Yard Measured Weight - Twin case updating FWD container

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2262.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2263.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2264.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2265.png)

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2266.png)

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



**6** **Updating Yard** **Measured Weight**

**6.4  Yard** **Measured W****eight Configuration**

 

 

***\*6.4\**** ***\*Yard\**** ***\*Measured\**** ***\*Weight\**** ***\*Configuration\****

This functionality should work out of the box. But you will need to make sure the yard measured weight attribute is dis- played in Xps-client. It is available under the "Contents" sub menu. If there is no such field then you need to add the   attribute below to the ***\*SPARCS settings\*******\*.txt\**** file.

Under the following section, add the tab delimited lines (better to open in EXCEL and modify)

\#ATTRIBUTE  code  # chars in use   long name  short name QWTY  4  Y  Scale Weight - Yard  Scale Wt.-Yard

Note that these fields are updated in Quintals and stored in Quintals internally, each Quintal is a 100 kg unit (so 10 Quintals is a Metric Ton)

When strings are displayed they are converted to kg (by multiplying them by 100)

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                           **N4 4.0.21: ECN4**



***\**7\**\*** ***\**Index\**\***

 

 



***\*7\**** ***\*Index\****

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2267.png)![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2268.png) 

***\*#\****

26-Code [136](#bookmark188)

***\*A\****

Acknowledgements [22](#bookmark227)

Anatomy of XMLRDT Response from ECN4 [17](#bookmark215) Area Status 2409 [32](#bookmark250)

Attributes [23](#bookmark231)

***\*C\****

CHE Auto Rehandle [56](#bookmark270)

CHE Become Available 2632 [28](#bookmark242)   CHE Become Unavailable 2632 [31](#bookmark246) CHE Cancel 2630 [35](#bookmark256)

CHE ChangeListMode 2727 [76](#bookmark300) CHE Clear Message 2630 [38](#bookmark260)

CHE Confirm Container 2631 [50](#bookmark266)

CHE Coordinate Conversion (not supported) [108](#bookmark325) CHE Dispatch TT [73](#bookmark294)

CHE Empty to Origin [70](#bookmark290)

CHE Joblist Promotion 2535 [67](#bookmark282) CHE Joblist Request 2724 [60](#bookmark276)  CHE Joblist Select 2630 [64](#bookmark278)

CHE Lift [42](#bookmark264)

CHE Log In 2632 [27](#bookmark240)  CHE Log Off 2632 [32](#bookmark248)

CHE Manual Rehandle 2630 [57](#bookmark272) CHE Park And Wait [88](#bookmark309)

CHE Park Laden Trailer [85](#bookmark319)

CHE Park Trailer 2630 [85](#bookmark307)

CHE Park Unladen Trailer [86](#bookmark321)

CHE Progress Update [59](#bookmark274)

CHE Pull Laden Trailer [81](#bookmark315)

CHE Pull Trailer 2630 [81](#bookmark305)

CHE Pull Unladen Trailer [83](#bookmark317)

CHE Search Truck Jobs [74](#bookmark296)

CHE Select Empty Delivery 2630 [69](#bookmark288) CHE Self-Assign 2640 [79](#bookmark302)

CHE Set [52](#bookmark268)

CHE Show Joblist Filter 2727 [67](#bookmark284) CHE Show Joblist Sort 2727 [68](#bookmark286) CHE Status 2609 [35](#bookmark254)

CHE Swap Position [74](#bookmark298)

CHETBD Merge [71](#bookmark292)

CHE Trailer Selection 2632 [29](#bookmark244) CHE Truck Departure 2630 [66](#bookmark280)

CHE TT Progress Update 2630 and 2633 [92](#bookmark311) CHE TT Signals Container Lifted [97](#bookmark313)

Container position elements (message types “1” – “5”) [134](#bookmark176)

Container Scales and Weight [138](#bookmark364)

Custom XMLRDT Client [8](#bookmark9)

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2269.png)

***\*E\****

EC Entities [10](#bookmark204)

ECN4 Actions [15](#bookmark213)

ECN4 Message Envelope [12](#bookmark208)

ECN4 Message Types [14](#bookmark211)

ECN4 XMLRDT Layers [7](#bookmark202)

ECN4 XMLRDT Message [13](#bookmark17)

Errors [23](#bookmark229)

Example Availability Messages [27](#bookmark234)

Example CHE Messages [42](#bookmark236)

Example Geodetic Messages [102](#bookmark323)

Example refId Lift [122](#bookmark343)

Example refId Messages [122](#bookmark338)

Example refID Position Update [123](#bookmark347)

Example refID Set [122](#bookmark345)

Example Terminal Tractor Messages using refID [123](#bookmark349) Example Truck (OTR) Messages using refID [124](#bookmark351)

Example TT Messages [80](#bookmark238)

Example XMLRDT Messages [26](#bookmark217)

Example Yard Measured Weight Messages [140](#bookmark368)

***\*F\****

Fail to Deck responses using refId [126](#bookmark353)

***\*G\****

Geodetic Configuration [116](#bookmark331)

Geodetic Positions [101](#bookmark127)

***\*J\****

Job List [19](#bookmark223)

Job List Response for nVMT [20](#bookmark31)

***\*L\****

Lift from Truck and Spatial Bin Filtering [113](#bookmark327)

***\*M\****

Message format for PDS to RDT messages [133](#bookmark174)

Message format for RDT to PDS messages [134](#bookmark178) Message Type [135](#bookmark180)

***\*N\****

N4 Landscape with ECN4 [7](#bookmark202)

Narrowband Components and Configuration [130](#bookmark357) Narrowband Serial PDS [128](#bookmark162)

Narrowband Support for ECN4 (ANSI Terminal) [129](#bookmark355) Narrowband User Interface [131](#bookmark359)

***\*O\****

Option-list [21](#bookmark225)

OTR Container Inventory Park 2709 [39](#bookmark262)

***\*P\****

Ping Message 2509 [34](#bookmark252) Pool [17](#bookmark219)



 

 

 

**Confidential** **and** **Proprietary**                                                         **N4** **4.0.21:** **ECN4**



 

 

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml300\wps2270.png) 

***\*R\****

refID Configuration [127](#bookmark340)

refID Positions [117](#bookmark139)

refID Syntax [118](#bookmark334)

***\*S\****

Serial Input Delimitter [136](#bookmark186)

Serial Message Format [133](#bookmark172)

Serial PDS [133](#bookmark361)

Special use cases concerning refID [120](#bookmark336)

Spreader Mode [135](#bookmark184)

Surrogate CHE Selection [37](#bookmark258)

***\*T\****

Tier Calculation [114](#bookmark329)

Truck variants (T, E) [118](#bookmark341)

***\*U\****

Updating Yard Measured Weight [137](#bookmark190), [139](#bookmark366)

***\*W\****

Work [18](#bookmark221)

***\*X\****

XMLRDT Channel [11](#bookmark206)

XMLRDT Functional Overview [6](#bookmark3)

***\*Y\****

Yard Measured Weight Configuration [142](#bookmark370)

Yard Stack Name [135](#bookmark182)

Yard variants (Y) [118](#bookmark334)

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

 

**Confidential** **and** **Proprietary**                                                         **N4** **4.0.21:** **ECN4**